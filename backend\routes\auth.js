const express = require('express');
const { body } = require('express-validator');
const {
  registerUser,
  loginUser,
  logoutUser,
  loginAdmin,
  getCurrentUser,
  getRoles
} = require('../controllers/authController');
const { authenticateUser } = require('../middleware/auth');

const router = express.Router();

// 用户注册申请验证规则
const registerValidation = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),
  body('roleId')
    .isInt({ min: 1 })
    .withMessage('请选择有效的角色'),
  body('realName')
    .isLength({ min: 2, max: 50 })
    .withMessage('真实姓名长度必须在2-50个字符之间'),
  body('phone')
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('请输入有效的手机号码'),
  body('companyName')
    .optional()
    .isLength({ max: 200 })
    .withMessage('公司名称长度不能超过200个字符'),
  body('applicationReason')
    .isLength({ min: 10, max: 500 })
    .withMessage('申请理由长度必须在10-500个字符之间')
];

// 登录验证规则
const loginValidation = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
];

// 路由定义
router.post('/register', registerValidation, registerUser);
router.post('/login', loginValidation, loginUser);
router.post('/logout', authenticateUser, logoutUser);
router.post('/admin/login', loginValidation, loginAdmin);
router.get('/me', authenticateUser, getCurrentUser);
router.get('/roles', getRoles);

module.exports = router;
