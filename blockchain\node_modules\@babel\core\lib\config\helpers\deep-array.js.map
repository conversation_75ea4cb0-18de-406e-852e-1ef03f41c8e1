{"version": 3, "names": ["finalize", "deepArr", "Object", "freeze", "flattenToSet", "arr", "result", "Set", "stack", "length", "el", "pop", "Array", "isArray", "push", "add"], "sources": ["../../../src/config/helpers/deep-array.ts"], "sourcesContent": ["export type DeepArray<T> = Array<T | ReadonlyDeepArray<T>>;\n\n// Just to make sure that DeepArray<T> is not assignable to ReadonlyDeepArray<T>\ndeclare const __marker: unique symbol;\nexport type ReadonlyDeepArray<T> = ReadonlyArray<T | ReadonlyDeepArray<T>> & {\n  [__marker]: true;\n};\n\nexport function finalize<T>(deepArr: DeepArray<T>): ReadonlyDeepArray<T> {\n  return Object.freeze(deepArr) as ReadonlyDeepArray<T>;\n}\n\nexport function flattenToSet<T extends string>(\n  arr: ReadonlyDeepArray<T>,\n): Set<T> {\n  const result = new Set<T>();\n  const stack = [arr];\n  while (stack.length > 0) {\n    for (const el of stack.pop()) {\n      if (Array.isArray(el)) stack.push(el as ReadonlyDeepArray<T>);\n      else result.add(el as T);\n    }\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;AAQO,SAASA,QAAQA,CAAIC,OAAqB,EAAwB;EACvE,OAAOC,MAAM,CAACC,MAAM,CAACF,OAAO,CAAC;AAC/B;AAEO,SAASG,YAAYA,CAC1BC,GAAyB,EACjB;EACR,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAI,CAAC;EAC3B,MAAMC,KAAK,GAAG,CAACH,GAAG,CAAC;EACnB,OAAOG,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IACvB,KAAK,MAAMC,EAAE,IAAIF,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE;MAC5B,IAAIC,KAAK,CAACC,OAAO,CAACH,EAAE,CAAC,EAAEF,KAAK,CAACM,IAAI,CAACJ,EAA0B,CAAC,CAAC,KACzDJ,MAAM,CAACS,GAAG,CAACL,EAAO,CAAC;IAC1B;EACF;EACA,OAAOJ,MAAM;AACf;AAAC", "ignoreList": []}