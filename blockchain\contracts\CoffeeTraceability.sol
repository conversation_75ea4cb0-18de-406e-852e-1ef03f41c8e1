// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract CoffeeTraceability {
    
    // 事件定义
    event BatchCreated(string indexed batchNumber, address indexed grower, uint256 timestamp);
    event BatchProcessed(string indexed batchNumber, address indexed processor, uint256 timestamp);
    event BatchDistributed(string indexed batchNumber, address indexed distributor, uint256 timestamp);
    event BatchSold(string indexed batchNumber, address indexed seller, address indexed buyer, uint256 timestamp);
    
    // 结构体定义
    struct CoffeeBatch {
        string batchNumber;
        address grower;
        string variety;
        string originLocation;
        uint256 plantingDate;
        uint256 harvestDate;
        string processingMethod;
        string qualityGrade;
        uint256 quantityKg;
        BatchStatus status;
        uint256 createdAt;
        bool exists;
    }
    
    struct ProcessingRecord {
        address processor;
        uint256 processingDate;
        string processingMethod;
        string roastLevel;
        string processingLocation;
        string qualityNotes;
        uint256 outputQuantityKg;
        uint256 timestamp;
    }
    
    struct DistributionRecord {
        address distributor;
        uint256 distributionDate;
        string destination;
        string transportMethod;
        string storageConditions;
        uint256 quantityKg;
        uint256 pricePerKg;
        uint256 timestamp;
    }
    
    struct SaleRecord {
        address seller;
        address buyer;
        uint256 saleDate;
        uint256 quantityKg;
        uint256 unitPrice;
        uint256 totalAmount;
        string paymentMethod;
        uint256 timestamp;
    }
    
    enum BatchStatus { Planted, Harvested, Processed, Distributed, Sold }
    
    // 状态变量
    mapping(string => CoffeeBatch) public coffeeBatches;
    mapping(string => ProcessingRecord[]) public processingRecords;
    mapping(string => DistributionRecord[]) public distributionRecords;
    mapping(string => SaleRecord[]) public salesRecords;
    mapping(address => bool) public authorizedUsers;
    
    address public owner;
    string[] public batchNumbers;
    
    // 修饰符
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
    
    modifier onlyAuthorized() {
        require(authorizedUsers[msg.sender] || msg.sender == owner, "Not authorized");
        _;
    }
    
    modifier batchExists(string memory _batchNumber) {
        require(coffeeBatches[_batchNumber].exists, "Batch does not exist");
        _;
    }
    
    // 构造函数
    constructor() {
        owner = msg.sender;
        authorizedUsers[msg.sender] = true;
    }
    
    // 授权用户
    function authorizeUser(address _user) external onlyOwner {
        authorizedUsers[_user] = true;
    }
    
    function revokeUser(address _user) external onlyOwner {
        authorizedUsers[_user] = false;
    }
    
    // 创建咖啡豆批次
    function createBatch(
        string memory _batchNumber,
        string memory _variety,
        string memory _originLocation,
        uint256 _plantingDate,
        uint256 _harvestDate,
        string memory _processingMethod,
        string memory _qualityGrade,
        uint256 _quantityKg
    ) external onlyAuthorized {
        require(!coffeeBatches[_batchNumber].exists, "Batch already exists");
        
        coffeeBatches[_batchNumber] = CoffeeBatch({
            batchNumber: _batchNumber,
            grower: msg.sender,
            variety: _variety,
            originLocation: _originLocation,
            plantingDate: _plantingDate,
            harvestDate: _harvestDate,
            processingMethod: _processingMethod,
            qualityGrade: _qualityGrade,
            quantityKg: _quantityKg,
            status: BatchStatus.Planted,
            createdAt: block.timestamp,
            exists: true
        });
        
        batchNumbers.push(_batchNumber);
        emit BatchCreated(_batchNumber, msg.sender, block.timestamp);
    }
    
    // 添加加工记录
    function addProcessingRecord(
        string memory _batchNumber,
        uint256 _processingDate,
        string memory _processingMethod,
        string memory _roastLevel,
        string memory _processingLocation,
        string memory _qualityNotes,
        uint256 _outputQuantityKg
    ) external onlyAuthorized batchExists(_batchNumber) {
        
        processingRecords[_batchNumber].push(ProcessingRecord({
            processor: msg.sender,
            processingDate: _processingDate,
            processingMethod: _processingMethod,
            roastLevel: _roastLevel,
            processingLocation: _processingLocation,
            qualityNotes: _qualityNotes,
            outputQuantityKg: _outputQuantityKg,
            timestamp: block.timestamp
        }));
        
        coffeeBatches[_batchNumber].status = BatchStatus.Processed;
        emit BatchProcessed(_batchNumber, msg.sender, block.timestamp);
    }
    
    // 添加分销记录
    function addDistributionRecord(
        string memory _batchNumber,
        uint256 _distributionDate,
        string memory _destination,
        string memory _transportMethod,
        string memory _storageConditions,
        uint256 _quantityKg,
        uint256 _pricePerKg
    ) external onlyAuthorized batchExists(_batchNumber) {
        
        distributionRecords[_batchNumber].push(DistributionRecord({
            distributor: msg.sender,
            distributionDate: _distributionDate,
            destination: _destination,
            transportMethod: _transportMethod,
            storageConditions: _storageConditions,
            quantityKg: _quantityKg,
            pricePerKg: _pricePerKg,
            timestamp: block.timestamp
        }));
        
        coffeeBatches[_batchNumber].status = BatchStatus.Distributed;
        emit BatchDistributed(_batchNumber, msg.sender, block.timestamp);
    }
    
    // 添加销售记录
    function addSaleRecord(
        string memory _batchNumber,
        address _buyer,
        uint256 _saleDate,
        uint256 _quantityKg,
        uint256 _unitPrice,
        uint256 _totalAmount,
        string memory _paymentMethod
    ) external onlyAuthorized batchExists(_batchNumber) {
        
        salesRecords[_batchNumber].push(SaleRecord({
            seller: msg.sender,
            buyer: _buyer,
            saleDate: _saleDate,
            quantityKg: _quantityKg,
            unitPrice: _unitPrice,
            totalAmount: _totalAmount,
            paymentMethod: _paymentMethod,
            timestamp: block.timestamp
        }));
        
        coffeeBatches[_batchNumber].status = BatchStatus.Sold;
        emit BatchSold(_batchNumber, msg.sender, _buyer, block.timestamp);
    }
    
    // 查询函数
    function getBatch(string memory _batchNumber) external view returns (CoffeeBatch memory) {
        require(coffeeBatches[_batchNumber].exists, "Batch does not exist");
        return coffeeBatches[_batchNumber];
    }
    
    function getProcessingRecords(string memory _batchNumber) external view returns (ProcessingRecord[] memory) {
        return processingRecords[_batchNumber];
    }
    
    function getDistributionRecords(string memory _batchNumber) external view returns (DistributionRecord[] memory) {
        return distributionRecords[_batchNumber];
    }
    
    function getSalesRecords(string memory _batchNumber) external view returns (SaleRecord[] memory) {
        return salesRecords[_batchNumber];
    }
    
    function getAllBatchNumbers() external view returns (string[] memory) {
        return batchNumbers;
    }
    
    function getBatchCount() external view returns (uint256) {
        return batchNumbers.length;
    }
}
