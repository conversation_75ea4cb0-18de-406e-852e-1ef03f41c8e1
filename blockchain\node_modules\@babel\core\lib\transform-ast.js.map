{"version": 3, "names": ["_gensync", "data", "require", "_index", "_index2", "_rewriteStackTrace", "transformFromAstRunner", "gens<PERSON>", "ast", "code", "opts", "config", "loadConfig", "Error", "run", "transformFromAst", "exports", "optsOrCallback", "maybe<PERSON><PERSON><PERSON>", "callback", "undefined", "beginHiddenCallStack", "sync", "errback", "transformFromAstSync", "args", "transformFromAstAsync", "async"], "sources": ["../src/transform-ast.ts"], "sourcesContent": ["import gensync, { type <PERSON><PERSON> } from \"gensync\";\n\nimport loadConfig from \"./config/index.ts\";\nimport type { InputOptions, ResolvedConfig } from \"./config/index.ts\";\nimport { run } from \"./transformation/index.ts\";\nimport type * as t from \"@babel/types\";\n\nimport { beginHiddenCallStack } from \"./errors/rewrite-stack-trace.ts\";\n\nimport type { FileResult, FileResultCallback } from \"./transformation/index.ts\";\ntype AstRoot = t.File | t.Program;\n\ntype TransformFromAst = {\n  (ast: AstRoot, code: string, callback: FileResultCallback): void;\n  (\n    ast: AstRoot,\n    code: string,\n    opts: InputOptions | undefined | null,\n    callback: FileResultCallback,\n  ): void;\n  (ast: AstRoot, code: string, opts?: InputOptions | null): FileResult | null;\n};\n\nconst transformFromAstRunner = gensync(function* (\n  ast: AstRoot,\n  code: string,\n  opts: InputOptions | undefined | null,\n): Handler<FileResult | null> {\n  const config: ResolvedConfig | null = yield* loadConfig(opts);\n  if (config === null) return null;\n\n  if (!ast) throw new Error(\"No AST given\");\n\n  return yield* run(config, code, ast);\n});\n\nexport const transformFromAst: TransformFromAst = function transformFromAst(\n  ast,\n  code,\n  optsOrCallback?: InputOptions | null | undefined | FileResultCallback,\n  maybeCallback?: FileResultCallback,\n) {\n  let opts: InputOptions | undefined | null;\n  let callback: FileResultCallback | undefined;\n  if (typeof optsOrCallback === \"function\") {\n    callback = optsOrCallback;\n    opts = undefined;\n  } else {\n    opts = optsOrCallback;\n    callback = maybeCallback;\n  }\n\n  if (callback === undefined) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'transformFromAst' function expects a callback. If you need to call it synchronously, please use 'transformFromAstSync'.\",\n      );\n    } else {\n      // console.warn(\n      //   \"Starting from Babel 8.0.0, the 'transformFromAst' function will expect a callback. If you need to call it synchronously, please use 'transformFromAstSync'.\",\n      // );\n      return beginHiddenCallStack(transformFromAstRunner.sync)(ast, code, opts);\n    }\n  }\n\n  beginHiddenCallStack(transformFromAstRunner.errback)(\n    ast,\n    code,\n    opts,\n    callback,\n  );\n};\n\nexport function transformFromAstSync(\n  ...args: Parameters<typeof transformFromAstRunner.sync>\n) {\n  return beginHiddenCallStack(transformFromAstRunner.sync)(...args);\n}\n\nexport function transformFromAstAsync(\n  ...args: Parameters<typeof transformFromAstRunner.async>\n) {\n  return beginHiddenCallStack(transformFromAstRunner.async)(...args);\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAE,MAAA,GAAAD,OAAA;AAEA,IAAAE,OAAA,GAAAF,OAAA;AAGA,IAAAG,kBAAA,GAAAH,OAAA;AAgBA,MAAMI,sBAAsB,GAAGC,SAAMA,CAAC,CAAC,WACrCC,GAAY,EACZC,IAAY,EACZC,IAAqC,EACT;EAC5B,MAAMC,MAA6B,GAAG,OAAO,IAAAC,cAAU,EAACF,IAAI,CAAC;EAC7D,IAAIC,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;EAEhC,IAAI,CAACH,GAAG,EAAE,MAAM,IAAIK,KAAK,CAAC,cAAc,CAAC;EAEzC,OAAO,OAAO,IAAAC,WAAG,EAACH,MAAM,EAAEF,IAAI,EAAED,GAAG,CAAC;AACtC,CAAC,CAAC;AAEK,MAAMO,gBAAkC,GAAAC,OAAA,CAAAD,gBAAA,GAAG,SAASA,gBAAgBA,CACzEP,GAAG,EACHC,IAAI,EACJQ,cAAqE,EACrEC,aAAkC,EAClC;EACA,IAAIR,IAAqC;EACzC,IAAIS,QAAwC;EAC5C,IAAI,OAAOF,cAAc,KAAK,UAAU,EAAE;IACxCE,QAAQ,GAAGF,cAAc;IACzBP,IAAI,GAAGU,SAAS;EAClB,CAAC,MAAM;IACLV,IAAI,GAAGO,cAAc;IACrBE,QAAQ,GAAGD,aAAa;EAC1B;EAEA,IAAIC,QAAQ,KAAKC,SAAS,EAAE;IAKnB;MAIL,OAAO,IAAAC,uCAAoB,EAACf,sBAAsB,CAACgB,IAAI,CAAC,CAACd,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;IAC3E;EACF;EAEA,IAAAW,uCAAoB,EAACf,sBAAsB,CAACiB,OAAO,CAAC,CAClDf,GAAG,EACHC,IAAI,EACJC,IAAI,EACJS,QACF,CAAC;AACH,CAAC;AAEM,SAASK,oBAAoBA,CAClC,GAAGC,IAAoD,EACvD;EACA,OAAO,IAAAJ,uCAAoB,EAACf,sBAAsB,CAACgB,IAAI,CAAC,CAAC,GAAGG,IAAI,CAAC;AACnE;AAEO,SAASC,qBAAqBA,CACnC,GAAGD,IAAqD,EACxD;EACA,OAAO,IAAAJ,uCAAoB,EAACf,sBAAsB,CAACqB,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;AACpE;AAAC", "ignoreList": []}