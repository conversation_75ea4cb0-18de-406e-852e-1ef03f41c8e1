{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light", "../src/index-minimal.js", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "1", "require", "module", "exports", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "char<PERSON>t", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "inquire", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "Float32Array", "f32", "f8b", "le", "writeFloat_f32_cpy", "val", "buf", "pos", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "writeFloat_ieee754", "writeUint", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "f64", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "moduleName", "mod", "eval", "e", "path", "isAbsolute", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "call", "utf8", "len", "read", "write", "c1", "c2", "converter", "Enum", "util", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "ref", "resolvedType", "values", "repeated", "typeDefault", "fullName", "isUnsigned", "type", "genValuePartial_toObject", "fromObject", "mtype", "fields", "fieldsArray", "name", "safeProp", "map", "arrayRef", "useToArray", "id", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "arrayDefault", "hasKs2", "_fieldsArray", "indexOf", "filter", "group", "keyType", "types", "basic", "packed", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "key", "preEncoded", "ReflectionObject", "create", "constructor", "className", "Namespace", "comment", "comments", "TypeError", "reserved", "fromJSON", "json", "enm", "toJSON", "toJSONOptions", "keepComments", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "rule", "extend", "isObject", "toLowerCase", "message", "defaultValue", "<PERSON>", "extensionField", "declaringField", "_packed", "defineProperty", "get", "getOption", "setOption", "ifNotSet", "resolved", "defaults", "parent", "lookupTypeOrEnum", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "configure", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Writer", "BufferWriter", "rpc", "roots", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "nested", "_nested<PERSON><PERSON>y", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "define", "isArray", "ptr", "part", "resolveAll", "lookup", "filterTypes", "parentAlreadyChecked", "found", "lookupEnum", "lookupService", "Service_", "Enum_", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "Root_", "fieldNames", "oneof", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "skip", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "parse", "common", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "getBundledFileName", "idx", "lastIndexOf", "altname", "substring", "process", "parsed", "imports", "weakImports", "queued", "weak", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "extendedType", "sisterField", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "method", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "service", "inherited", "methodsArray", "rpcService", "methodName", "lcFirst", "isReserved", "m", "q", "s", "oneofs", "extensions", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "wrapper", "originalThis", "fork", "l<PERSON>im", "typeName", "target", "bake", "o", "safePropBackslashRe", "safePropQuoteRe", "ucFirst", "str", "toUpperCase", "camelCaseRe", "camelCase", "a", "decorateRoot", "enumerable", "decorateEnumIndex", "zero", "zzEncode", "zeroHash", "from", "parseInt", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "dst", "src", "newError", "CustomError", "captureStackTrace", "stack", "pool", "global", "window", "versions", "node", "Number", "isFinite", "isset", "isSet", "hasOwnProperty", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "env", "ENABLE_LONG", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "invalid", "genVerifyKey", "genVerifyValue", "oneofProp", "expected", "type_url", "substr", "Op", "next", "noop", "State", "head", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeBytesBuffer", "copy", "writeStringBuffer", "byteLength", "$require", "$module", "amd", "isLong"], "mappings": ";;;;;CAAA,SAAAA,GAAA,aAAA,IAAAC,EAAAC,EAAAC,EAcAC,EAdAH,EAiCA,CAAAI,EAAA,CAAA,SAAAC,EAAAC,GChCAA,EAAAC,QAmBA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAC,EAAA,EACAC,GAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,KAAAF,UAAAG,KACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,GAAA,EACAI,EACAD,EAAAC,OACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,KAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,KAIA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,GACA,MAAAU,GACAJ,IACAA,GAAA,EACAG,EAAAC,gCCxCA,IAAAE,EAAAf,EAOAe,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,IAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,IAAAD,EAAA,GAAA,MAAAD,EAAAG,OAAAF,MACAC,EACA,OAAAE,KAAAC,KAAA,EAAAL,EAAAV,QAAA,EAAAY,GAUA,IANA,IAAAI,EAAAlB,MAAA,IAGAmB,EAAAnB,MAAA,KAGAoB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,IASAT,EAAAU,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,KACA,OAAAK,GACA,KAAA,EACAD,EAAAP,KAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,KAAAF,EAAA,GAAAW,GACAD,EAAA,EAGA,KAAAR,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GASA,OANAQ,IACAD,EAAAP,KAAAF,EAAAO,GACAE,EAAAP,KAAA,GACA,IAAAQ,IACAD,EAAAP,KAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAGA,IAAAe,EAAA,mBAUAxB,EAAAyB,OAAA,SAAAxB,EAAAU,EAAAnB,GAIA,IAHA,IAEAsB,EAFAF,EAAApB,EACAyB,EAAA,EAEAR,EAAA,EAAAA,EAAAR,EAAAV,QAAA,CACA,IAAAmC,EAAAzB,EAAA0B,WAAAlB,KACA,GAAA,KAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAAjD,EACA,MAAAmD,MAAAJ,GACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,KAAAsB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,GAAAsB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,EAAAsB,IAAA,EAAAY,EACAT,EAAA,GAIA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,GACA,OAAAhC,EAAAoB,GAQAZ,EAAA6B,KAAA,SAAA5B,GACA,MAAA,mEAAA4B,KAAA5B,0BC/HA,SAAA6B,EAAAC,EAAAC,GAGA,iBAAAD,IACAC,EAAAD,EACAA,EAAAtD,GAGA,IAAAwD,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,iBAAAA,EAAA,CACA,IAAAC,EAAAC,IAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,GACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,GACAS,EAAAvD,MAAAoD,EAAAlD,OAAA,GACAsD,EAAAxD,MAAAoD,EAAAlD,QACAuD,EAAA,EACAA,EAAAL,EAAAlD,QACAqD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,MAGA,OADAF,EAAAE,GAAAV,EACAW,SAAAhD,MAAA,KAAA6C,GAAA7C,MAAA,KAAA8C,GAEA,OAAAE,SAAAX,EAAAW,GAMA,IAFA,IAAAC,EAAA3D,MAAAC,UAAAC,OAAA,GACA0D,EAAA,EACAA,EAAAD,EAAAzD,QACAyD,EAAAC,GAAA3D,YAAA2D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,KACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,OAAAC,EAAAjC,GACA,IAAA,IAAA,OAAAf,KAAAiD,MAAAD,GAAAjC,GACA,IAAA,IAAA,OAAAmC,KAAAC,UAAAH,GACA,IAAA,IAAA,OAAAA,EAAAjC,GAEA,MAAA,MAEA6B,IAAAD,EAAAzD,OACA,MAAAqC,MAAA,4BAEA,OADAK,EAAAd,KAAAgB,GACAD,EAGA,SAAAG,EAAAoB,GACA,MAAA,aAAAA,GAAAzB,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,MAAA,IAAA,SAAAU,EAAAV,KAAA,QAAA,MAIA,OADAW,EAAAG,SAAAA,EACAH,GAhFAlD,EAAAC,QAAA6C,GAiGAQ,SAAA,wBCzFA,SAAAoB,IAOAC,KAAAC,EAAA,IAfA5E,EAAAC,QAAAyE,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA5C,KAAA,CACAjC,GAAAA,EACAC,IAAAA,GAAAwE,OAEAA,MASAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAtF,EACAkF,KAAAC,EAAA,QAEA,GAAA1E,IAAAT,EACAkF,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAtD,EAAA,EAAAA,EAAAwD,EAAA1E,QACA0E,EAAAxD,GAAAvB,KAAAA,EACA+E,EAAAC,OAAAzD,EAAA,KAEAA,EAGA,OAAAkD,MASAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA3D,EAAA,EACAA,EAAAnB,UAAAC,QACA6E,EAAAjD,KAAA7B,UAAAmB,MACA,IAAAA,EAAA,EAAAA,EAAAwD,EAAA1E,QACA0E,EAAAxD,GAAAvB,GAAAa,MAAAkE,EAAAxD,KAAAtB,IAAAiF,GAEA,OAAAT,4BCzEA3E,EAAAC,QAAAoF,EAEA,IAAAC,EAAAvF,EAAA,GAGAwF,EAFAxF,EAAA,EAEAyF,CAAA,MA2BA,SAAAH,EAAAI,EAAAC,EAAAC,GAOA,MANA,mBAAAD,GACAC,EAAAD,EACAA,EAAA,IACAA,IACAA,EAAA,IAEAC,GAIAD,EAAAE,KAAAL,GAAAA,EAAAM,SACAN,EAAAM,SAAAJ,EAAA,SAAA3E,EAAAgF,GACA,OAAAhF,GAAA,oBAAAiF,eACAV,EAAAO,IAAAH,EAAAC,EAAAC,GACA7E,EACA6E,EAAA7E,GACA6E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAAzC,SAAA,WAIAgC,EAAAO,IAAAH,EAAAC,EAAAC,GAbAL,EAAAD,EAAAV,KAAAc,EAAAC,GAqCAL,EAAAO,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAAzG,EAKA,GAAA,IAAAmG,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAA/C,MAAA,UAAAgD,EAAAO,SAIA,GAAAT,EAAAM,OAAA,CACA,IAAArE,EAAAiE,EAAAQ,SACA,IAAAzE,EAAA,CACAA,EAAA,GACA,IAAA,IAAAF,EAAA,EAAAA,EAAAmE,EAAAS,aAAA9F,SAAAkB,EACAE,EAAAQ,KAAA,IAAAyD,EAAAS,aAAA1D,WAAAlB,IAEA,OAAAkE,EAAA,KAAA,oBAAAW,WAAA,IAAAA,WAAA3E,GAAAA,GAEA,OAAAgE,EAAA,KAAAC,EAAAS,eAGAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,sCACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,GACAG,EAAAc,qCC1BA,SAAAC,EAAA1G,GAwNA,MArNA,oBAAA2G,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAE,EAAA,IAAAR,WAAAO,EAAAlF,QACAoF,EAAA,MAAAD,EAAA,GAEA,SAAAE,EAAAC,EAAAC,EAAAC,GACAN,EAAA,GAAAI,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAGA,SAAAM,EAAAH,EAAAC,EAAAC,GACAN,EAAA,GAAAI,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAQA,SAAAO,EAAAH,EAAAC,GAKA,OAJAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAN,EAAA,GAGA,SAAAS,EAAAJ,EAAAC,GAKA,OAJAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAN,EAAA,GAjBA5G,EAAAsH,aAAAR,EAAAC,EAAAI,EAEAnH,EAAAuH,aAAAT,EAAAK,EAAAJ,EAmBA/G,EAAAwH,YAAAV,EAAAM,EAAAC,EAEArH,EAAAyH,YAAAX,EAAAO,EAAAD,EA9CA,GAiDA,WAEA,SAAAM,EAAAC,EAAAX,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,QACA,GAAAW,MAAAb,GACAW,EAAA,WAAAV,EAAAC,QACA,GAAA,qBAAAF,EACAW,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,QACA,GAAAF,EAAA,sBACAW,GAAAC,GAAA,GAAAxG,KAAA0G,MAAAd,EAAA,yBAAA,EAAAC,EAAAC,OACA,CACA,IAAAa,EAAA3G,KAAAiD,MAAAjD,KAAAmC,IAAAyD,GAAA5F,KAAA4G,KAEAL,GAAAC,GAAA,GAAAG,EAAA,KAAA,GADA,QAAA3G,KAAA0G,MAAAd,EAAA5F,KAAA6G,IAAA,GAAAF,GAAA,YACA,EAAAd,EAAAC,IAOA,SAAAgB,EAAAC,EAAAlB,EAAAC,GACA,IAAAkB,EAAAD,EAAAlB,EAAAC,GACAU,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,EAAA,QAAAD,EACA,OAAA,MAAAL,EACAM,EACAC,IACAV,GAAAW,EAAAA,GACA,IAAAR,EACA,qBAAAH,EAAAS,EACAT,EAAAxG,KAAA6G,IAAA,EAAAF,EAAA,MAAAM,EAAA,SAdArI,EAAAsH,aAAAI,EAAAc,KAAA,KAAAC,GACAzI,EAAAuH,aAAAG,EAAAc,KAAA,KAAAE,GAgBA1I,EAAAwH,YAAAU,EAAAM,KAAA,KAAAG,GACA3I,EAAAyH,YAAAS,EAAAM,KAAA,KAAAI,GAvCA,GA4CA,oBAAAC,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAhC,EAAA,IAAAR,WAAAyC,EAAApH,QACAoF,EAAA,MAAAD,EAAA,GAEA,SAAAkC,EAAA/B,EAAAC,EAAAC,GACA4B,EAAA,GAAA9B,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAGA,SAAAmC,EAAAhC,EAAAC,EAAAC,GACA4B,EAAA,GAAA9B,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAQA,SAAAoC,EAAAhC,EAAAC,GASA,OARAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACA4B,EAAA,GAGA,SAAAI,EAAAjC,EAAAC,GASA,OARAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACA4B,EAAA,GAzBA9I,EAAAmJ,cAAArC,EAAAiC,EAAAC,EAEAhJ,EAAAoJ,cAAAtC,EAAAkC,EAAAD,EA2BA/I,EAAAqJ,aAAAvC,EAAAmC,EAAAC,EAEAlJ,EAAAsJ,aAAAxC,EAAAoC,EAAAD,EA9DA,GAiEA,WAEA,SAAAM,EAAA5B,EAAA6B,EAAAC,EAAAzC,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,EAAAuC,QACA,GAAA5B,MAAAb,GACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,EAAA,WAAAV,EAAAC,EAAAuC,QACA,GAAA,sBAAAzC,EACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,EAAAuC,OACA,CACA,IAAApB,EACA,GAAArB,EAAA,uBAEAW,GADAU,EAAArB,EAAA,UACA,EAAAC,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAAS,EAAA,cAAA,EAAApB,EAAAC,EAAAuC,OACA,CACA,IAAA1B,EAAA3G,KAAAiD,MAAAjD,KAAAmC,IAAAyD,GAAA5F,KAAA4G,KACA,OAAAD,IACAA,EAAA,MAEAJ,EAAA,kBADAU,EAAArB,EAAA5F,KAAA6G,IAAA,GAAAF,MACA,EAAAd,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAApB,EAAAC,EAAAuC,KAQA,SAAAC,EAAAvB,EAAAqB,EAAAC,EAAAxC,EAAAC,GACA,IAAAyC,EAAAxB,EAAAlB,EAAAC,EAAAsC,GACAI,EAAAzB,EAAAlB,EAAAC,EAAAuC,GACA7B,EAAA,GAAAgC,GAAA,IAAA,EACA7B,EAAA6B,IAAA,GAAA,KACAvB,EAAA,YAAA,QAAAuB,GAAAD,EACA,OAAA,OAAA5B,EACAM,EACAC,IACAV,GAAAW,EAAAA,GACA,IAAAR,EACA,OAAAH,EAAAS,EACAT,EAAAxG,KAAA6G,IAAA,EAAAF,EAAA,OAAAM,EAAA,kBAfArI,EAAAmJ,cAAAI,EAAAf,KAAA,KAAAC,EAAA,EAAA,GACAzI,EAAAoJ,cAAAG,EAAAf,KAAA,KAAAE,EAAA,EAAA,GAiBA1I,EAAAqJ,aAAAK,EAAAlB,KAAA,KAAAG,EAAA,EAAA,GACA3I,EAAAsJ,aAAAI,EAAAlB,KAAA,KAAAI,EAAA,EAAA,GAnDA,GAuDA5I,EAKA,SAAAyI,EAAAzB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAGA,SAAA0B,EAAA1B,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,EAGA,SAAA2B,EAAA1B,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,EAGA,SAAA0B,EAAA3B,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,EA3UAnH,EAAAC,QAAA0G,EAAAA,2BCOA,SAAAnB,EAAAsE,GACA,IACA,IAAAC,EAAAC,KAAA,UAAAA,CAAAF,GACA,GAAAC,IAAAA,EAAAxJ,QAAAmD,OAAAC,KAAAoG,GAAAxJ,QACA,OAAAwJ,EACA,MAAAE,IACA,OAAA,KAdAjK,EAAAC,QAAAuF,0BCMA,IAAA0E,EAAAjK,EAEAkK,EAMAD,EAAAC,WAAA,SAAAD,GACA,MAAA,eAAArH,KAAAqH,IAGAE,EAMAF,EAAAE,UAAA,SAAAF,GAGA,IAAAnI,GAFAmI,EAAAA,EAAAhG,QAAA,MAAA,KACAA,QAAA,UAAA,MACAmG,MAAA,KACAC,EAAAH,EAAAD,GACAK,EAAA,GACAD,IACAC,EAAAxI,EAAAyI,QAAA,KACA,IAAA,IAAA/I,EAAA,EAAAA,EAAAM,EAAAxB,QACA,OAAAwB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAmD,SAAAzD,EAAA,GACA6I,EACAvI,EAAAmD,OAAAzD,EAAA,KAEAA,EACA,MAAAM,EAAAN,GACAM,EAAAmD,OAAAzD,EAAA,KAEAA,EAEA,OAAA8I,EAAAxI,EAAAQ,KAAA,MAUA2H,EAAAtJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,IACAP,EAAAO,GACAA,GACAC,IACAF,EAAAL,EAAAK,KACAA,EAAAA,EAAAvG,QAAA,iBAAA,KAAA3D,OAAA6J,EAAAK,EAAA,IAAAC,GAAAA,0BC9DA1K,EAAAC,QA6BA,SAAA2K,EAAAtI,EAAAuI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,GACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,GACAtK,EAAA,GAEA,IAAA0G,EAAA5E,EAAA2I,KAAAD,EAAAxK,EAAAA,GAAAqK,GAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACA0G,6BCtCA,IAAAgE,EAAAjL,EAOAiL,EAAA3K,OAAA,SAAAU,GAGA,IAFA,IAAAkK,EAAA,EACAzI,EAAA,EACAjB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACAiB,EAAAzB,EAAA0B,WAAAlB,IACA,IACA0J,GAAA,EACAzI,EAAA,KACAyI,GAAA,EACA,QAAA,MAAAzI,IAAA,QAAA,MAAAzB,EAAA0B,WAAAlB,EAAA,OACAA,EACA0J,GAAA,GAEAA,GAAA,EAEA,OAAAA,GAUAD,EAAAE,KAAA,SAAAzJ,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,MACA,IACAI,EAAAP,KAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,MAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,KACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,OAAA,IAAA,GAAAD,EAAAC,OAAA,EAAA,GAAAD,EAAAC,MAAA,MACAI,EAAAP,KAAA,OAAAK,GAAA,IACAE,EAAAP,KAAA,OAAA,KAAAK,IAEAE,EAAAP,MAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,OAAA,EAAA,GAAAD,EAAAC,KACA,KAAAH,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAUAyJ,EAAAG,MAAA,SAAApK,EAAAU,EAAAnB,GAIA,IAHA,IACA8K,EACAC,EAFA3J,EAAApB,EAGAiB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACA6J,EAAArK,EAAA0B,WAAAlB,IACA,IACAE,EAAAnB,KAAA8K,GACAA,EAAA,KACA3J,EAAAnB,KAAA8K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAAtK,EAAA0B,WAAAlB,EAAA,MACA6J,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KACA9J,EACAE,EAAAnB,KAAA8K,GAAA,GAAA,IACA3J,EAAAnB,KAAA8K,GAAA,GAAA,GAAA,KAIA3J,EAAAnB,KAAA8K,GAAA,GAAA,IAHA3J,EAAAnB,KAAA8K,GAAA,EAAA,GAAA,KANA3J,EAAAnB,KAAA,GAAA8K,EAAA,KAcA,OAAA9K,EAAAoB,4BClGA,IAAA4J,EAAAvL,EAEAwL,EAAA1L,EAAA,IACA2L,EAAA3L,EAAA,IAWA,SAAA4L,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAKA,GAHAA,IAAAvM,IACAuM,EAAA,IAAAD,GAEAF,EAAAI,aACA,GAAAJ,EAAAI,wBAAAR,EAAA,CAAAG,EACA,cAAAI,GACA,IAAA,IAAAE,EAAAL,EAAAI,aAAAC,OAAAvI,EAAAD,OAAAC,KAAAuI,GAAAzK,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAoK,EAAAM,UAAAD,EAAAvI,EAAAlC,MAAAoK,EAAAO,aAAAR,EACA,YACAA,EACA,UAAAjI,EAAAlC,GADAmK,CAEA,WAAAM,EAAAvI,EAAAlC,IAFAmK,CAGA,SAAAG,EAAAG,EAAAvI,EAAAlC,IAHAmK,CAIA,SACAA,EACA,UACAA,EACA,2BAAAI,EADAJ,CAEA,sBAAAC,EAAAQ,SAAA,oBAFAT,CAGA,+BAAAG,EAAAD,EAAAE,OACA,CACA,IAAAM,GAAA,EACA,OAAAT,EAAAU,MACA,IAAA,SACA,IAAA,QAAAX,EACA,iBAAAG,EAAAC,GACA,MACA,IAAA,SACA,IAAA,UAAAJ,EACA,aAAAG,EAAAC,GACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAJ,EACA,WAAAG,EAAAC,GACA,MACA,IAAA,SACAM,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAV,EACA,gBADAA,CAEA,4CAAAG,EAAAC,EAAAM,EAFAV,CAGA,gCAAAI,EAHAJ,CAIA,sBAAAG,EAAAC,EAJAJ,CAKA,gCAAAI,EALAJ,CAMA,SAAAG,EAAAC,EANAJ,CAOA,gCAAAI,EAPAJ,CAQA,6DAAAG,EAAAC,EAAAA,EAAAM,EAAA,OAAA,IACA,MACA,IAAA,QAAAV,EACA,2BAAAI,EADAJ,CAEA,sEAAAI,EAAAD,EAAAC,EAFAJ,CAGA,qBAAAI,EAHAJ,CAIA,SAAAG,EAAAC,GACA,MACA,IAAA,SAAAJ,EACA,iBAAAG,EAAAC,GACA,MACA,IAAA,OAAAJ,EACA,kBAAAG,EAAAC,IAOA,OAAAJ,EA2EA,SAAAY,EAAAZ,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAI,aACAJ,EAAAI,wBAAAR,EAAAG,EACA,iDAAAG,EAAAD,EAAAC,EAAAA,GACAH,EACA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAO,GAAA,EACA,OAAAT,EAAAU,MACA,IAAA,SACA,IAAA,QAAAX,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,GACA,MACA,IAAA,SACAO,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAV,EACA,4BAAAG,EADAH,CAEA,uCAAAG,EAAAA,EAAAA,EAFAH,CAGA,OAHAA,CAIA,4IAAAG,EAAAA,EAAAA,EAAAA,EAAAO,EAAA,OAAA,GAAAP,GACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,GACA,MACA,QAAAH,EACA,UAAAG,EAAAA,IAIA,OAAAH,EApGAJ,EAAAiB,WAAA,SAAAC,GAEA,IAAAC,EAAAD,EAAAE,YACAhB,EAAAF,EAAA5I,QAAA,CAAA,KAAA4J,EAAAG,KAAA,cAAAnB,CACA,6BADAA,CAEA,YACA,IAAAiB,EAAApM,OAAA,OAAAqL,EACA,wBACAA,EACA,uBACA,IAAA,IAAAnK,EAAA,EAAAA,EAAAkL,EAAApM,SAAAkB,EAAA,CACA,IAAAoK,EAAAc,EAAAlL,GAAAb,UACAmL,EAAAL,EAAAoB,SAAAjB,EAAAgB,MAGA,GAAAhB,EAAAkB,IAAAnB,EACA,WAAAG,EADAH,CAEA,4BAAAG,EAFAH,CAGA,sBAAAC,EAAAQ,SAAA,oBAHAT,CAIA,SAAAG,EAJAH,CAKA,oDAAAG,GACAJ,EAAAC,EAAAC,EAAApK,EAAAsK,EAAA,UAAAJ,CACA,IADAA,CAEA,UAGA,GAAAE,EAAAM,SAAA,CACAP,EAAA,WAAAG,GACA,IAAAiB,EAAA,IAAAjB,EACAF,EAAAoB,eAEArB,EAAA,SADAoB,EAAA,QAAAnB,EAAAqB,IAEAtB,EAAA,uEACAG,EAAAA,EAAAiB,EAAAjB,EAAAiB,EAAAjB,IAEAH,EACA,yBAAAoB,EADApB,CAEA,sBAAAC,EAAAQ,SAAA,mBAFAT,CAGA,SAAAG,EAHAH,CAIA,gCAAAoB,GACArB,EAAAC,EAAAC,EAAApK,EAAAsK,EAAA,MAAAiB,EAAA,MAAArB,CACA,IADAA,CAEA,UAIAE,EAAAI,wBAAAR,GAAAG,EACA,iBAAAG,GACAJ,EAAAC,EAAAC,EAAApK,EAAAsK,GACAF,EAAAI,wBAAAR,GAAAG,EACA,KAEA,OAAAA,EACA,aAwDAJ,EAAA2B,SAAA,SAAAT,GAEA,IAAAC,EAAAD,EAAAE,YAAAtK,QAAA8K,KAAA1B,EAAA2B,mBACA,IAAAV,EAAApM,OACA,OAAAmL,EAAA5I,SAAA4I,CAAA,aAUA,IATA,IAAAE,EAAAF,EAAA5I,QAAA,CAAA,IAAA,KAAA4J,EAAAG,KAAA,YAAAnB,CACA,SADAA,CAEA,OAFAA,CAGA,YAEA4B,EAAA,GACAC,EAAA,GACAC,EAAA,GACA/L,EAAA,EACAA,EAAAkL,EAAApM,SAAAkB,EACAkL,EAAAlL,GAAAgM,SACAd,EAAAlL,GAAAb,UAAAuL,SAAAmB,EACAX,EAAAlL,GAAAsL,IAAAQ,EACAC,GAAArL,KAAAwK,EAAAlL,IAEA,GAAA6L,EAAA/M,OAAA,CAEA,IAFAqL,EACA,6BACAnK,EAAA,EAAAA,EAAA6L,EAAA/M,SAAAkB,EAAAmK,EACA,SAAAF,EAAAoB,SAAAQ,EAAA7L,GAAAoL,OACAjB,EACA,KAGA,GAAA2B,EAAAhN,OAAA,CAEA,IAFAqL,EACA,8BACAnK,EAAA,EAAAA,EAAA8L,EAAAhN,SAAAkB,EAAAmK,EACA,SAAAF,EAAAoB,SAAAS,EAAA9L,GAAAoL,OACAjB,EACA,KAGA,GAAA4B,EAAAjN,OAAA,CAEA,IAFAqL,EACA,mBACAnK,EAAA,EAAAA,EAAA+L,EAAAjN,SAAAkB,EAAA,CACA,IAAAoK,EAAA2B,EAAA/L,GACAsK,EAAAL,EAAAoB,SAAAjB,EAAAgB,MACA,GAAAhB,EAAAI,wBAAAR,EAAAG,EACA,6BAAAG,EAAAF,EAAAI,aAAAyB,WAAA7B,EAAAO,aAAAP,EAAAO,kBACA,GAAAP,EAAA8B,KAAA/B,EACA,iBADAA,CAEA,gCAAAC,EAAAO,YAAAwB,IAAA/B,EAAAO,YAAAyB,KAAAhC,EAAAO,YAAA0B,SAFAlC,CAGA,oEAAAG,EAHAH,CAIA,QAJAA,CAKA,6BAAAG,EAAAF,EAAAO,YAAA/I,WAAAwI,EAAAO,YAAA2B,iBACA,GAAAlC,EAAAmC,MAAA,CACA,IAAAC,EAAA,IAAA5N,MAAAwE,UAAAvC,MAAA2I,KAAAY,EAAAO,aAAA7J,KAAA,KAAA,IACAqJ,EACA,6BAAAG,EAAA3J,OAAAC,aAAAtB,MAAAqB,OAAAyJ,EAAAO,aADAR,CAEA,QAFAA,CAGA,SAAAG,EAAAkC,EAHArC,CAIA,6CAAAG,EAAAA,EAJAH,CAKA,UACAA,EACA,SAAAG,EAAAF,EAAAO,aACAR,EACA,KAEA,IAAAsC,GAAA,EACA,IAAAzM,EAAA,EAAAA,EAAAkL,EAAApM,SAAAkB,EAAA,CACAoK,EAAAc,EAAAlL,GAAA,IACAhB,EAAAiM,EAAAyB,EAAAC,QAAAvC,GACAE,EAAAL,EAAAoB,SAAAjB,EAAAgB,MACAhB,EAAAkB,KACAmB,IAAAA,GAAA,EAAAtC,EACA,YACAA,EACA,0CAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,kCACAY,EAAAZ,EAAAC,EAAApL,EAAAsL,EAAA,WAAAS,CACA,MACAX,EAAAM,UAAAP,EACA,uBAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,iCAAAG,GACAS,EAAAZ,EAAAC,EAAApL,EAAAsL,EAAA,MAAAS,CACA,OACAZ,EACA,uCAAAG,EAAAF,EAAAgB,MACAL,EAAAZ,EAAAC,EAAApL,EAAAsL,GACAF,EAAA4B,QAAA7B,EACA,eADAA,CAEA,SAAAF,EAAAoB,SAAAjB,EAAA4B,OAAAZ,MAAAhB,EAAAgB,OAEAjB,EACA,KAEA,OAAAA,EACA,+CC5SA5L,EAAAC,QAeA,SAAAyM,GAEA,IAAAd,EAAAF,EAAA5I,QAAA,CAAA,IAAA,KAAA4J,EAAAG,KAAA,UAAAnB,CACA,6BADAA,CAEA,qBAFAA,CAGA,qDAAAgB,EAAAE,YAAAyB,OAAA,SAAAxC,GAAA,OAAAA,EAAAkB,MAAAxM,OAAA,KAAA,IAHAmL,CAIA,kBAJAA,CAKA,oBACAgB,EAAA4B,OAAA1C,EACA,gBADAA,CAEA,SACAA,EACA,kBAGA,IADA,IAAAnK,EAAA,EACAA,EAAAiL,EAAAE,YAAArM,SAAAkB,EAAA,CACA,IAAAoK,EAAAa,EAAAyB,EAAA1M,GAAAb,UACA2L,EAAAV,EAAAI,wBAAAR,EAAA,QAAAI,EAAAU,KACAP,EAAA,IAAAN,EAAAoB,SAAAjB,EAAAgB,MAAAjB,EACA,WAAAC,EAAAqB,IAGArB,EAAAkB,KAAAnB,EACA,iBADAA,CAEA,4BAAAI,EAFAJ,CAGA,QAAAI,EAHAJ,CAIA,WAAAC,EAAA0C,QAJA3C,CAKA,WACA4C,EAAAb,KAAA9B,EAAA0C,WAAA9O,EACA+O,EAAAC,MAAAlC,KAAA9M,EAAAmM,EACA,8EAAAI,EAAAvK,GACAmK,EACA,sDAAAI,EAAAO,GAEAiC,EAAAC,MAAAlC,KAAA9M,EAAAmM,EACA,uCAAAI,EAAAvK,GACAmK,EACA,eAAAI,EAAAO,IAIAV,EAAAM,UAAAP,EAEA,uBAAAI,EAAAA,EAFAJ,CAGA,QAAAI,GAGAwC,EAAAE,OAAAnC,KAAA9M,GAAAmM,EACA,iBADAA,CAEA,0BAFAA,CAGA,kBAHAA,CAIA,kBAAAI,EAAAO,EAJAX,CAKA,SAGA4C,EAAAC,MAAAlC,KAAA9M,EAAAmM,EAAAC,EAAAI,aAAAqC,MACA,+BACA,0CAAAtC,EAAAvK,GACAmK,EACA,kBAAAI,EAAAO,IAGAiC,EAAAC,MAAAlC,KAAA9M,EAAAmM,EAAAC,EAAAI,aAAAqC,MACA,yBACA,oCAAAtC,EAAAvK,GACAmK,EACA,YAAAI,EAAAO,GACAX,EACA,SAWA,IATAA,EACA,WADAA,CAEA,kBAFAA,CAGA,QAHAA,CAKA,IALAA,CAMA,KAGAnK,EAAA,EAAAA,EAAAiL,EAAAyB,EAAA5N,SAAAkB,EAAA,CACA,IAAAkN,EAAAjC,EAAAyB,EAAA1M,GACAkN,EAAAC,UAAAhD,EACA,4BAAA+C,EAAA9B,KADAjB,CAEA,4CA3FA,qBA2FA+C,EA3FA9B,KAAA,KA8FA,OAAAjB,EACA,aApGA,IAAAH,EAAA1L,EAAA,IACAyO,EAAAzO,EAAA,IACA2L,EAAA3L,EAAA,4CCJAC,EAAAC,QAuCA,SAAAyM,GAWA,IATA,IAIAV,EAJAJ,EAAAF,EAAA5I,QAAA,CAAA,IAAA,KAAA4J,EAAAG,KAAA,UAAAnB,CACA,SADAA,CAEA,qBAKAiB,EAAAD,EAAAE,YAAAtK,QAAA8K,KAAA1B,EAAA2B,mBAEA5L,EAAA,EAAAA,EAAAkL,EAAApM,SAAAkB,EAAA,CACA,IAAAoK,EAAAc,EAAAlL,GAAAb,UACAH,EAAAiM,EAAAyB,EAAAC,QAAAvC,GACAU,EAAAV,EAAAI,wBAAAR,EAAA,QAAAI,EAAAU,KACAsC,EAAAL,EAAAC,MAAAlC,GAIA,GAHAP,EAAA,IAAAN,EAAAoB,SAAAjB,EAAAgB,MAGAhB,EAAAkB,IACAnB,EACA,kDAAAI,EAAAH,EAAAgB,KADAjB,CAEA,mDAAAI,EAFAJ,CAGA,4CAAAC,EAAAqB,IAAA,EAAA,KAAA,EAAA,EAAAsB,EAAAM,OAAAjD,EAAA0C,SAAA1C,EAAA0C,SACAM,IAAApP,EAAAmM,EACA,oEAAAnL,EAAAuL,GACAJ,EACA,qCAAA,GAAAiD,EAAAtC,EAAAP,GACAJ,EACA,IADAA,CAEA,UAGA,GAAAC,EAAAM,SAAA,CACA,IAAAa,EAAAhB,EACAH,EAAAoB,eACAD,EAAA,QAAAnB,EAAAqB,GACAtB,EAAA,SAAAoB,GACApB,EAAA,mEACAI,EAAAA,EAAAgB,EAAAhB,EAAAgB,EAAAhB,IAEAJ,EAAA,2BAAAoB,EAAAA,GAEAnB,EAAA6C,QAAAF,EAAAE,OAAAnC,KAAA9M,EAAAmM,EAEA,uBAAAC,EAAAqB,IAAA,EAAA,KAAA,EAFAtB,CAGA,+BAAAoB,EAHApB,CAIA,cAAAW,EAAAS,EAJApB,CAKA,eAGAA,EAEA,+BAAAoB,GACA6B,IAAApP,EACAsP,EAAAnD,EAAAC,EAAApL,EAAAuM,EAAA,OACApB,EACA,0BAAAC,EAAAqB,IAAA,EAAA2B,KAAA,EAAAtC,EAAAS,IAEApB,EACA,UAIAC,EAAAmD,UAAApD,EACA,iDAAAI,EAAAH,EAAAgB,MAEAgC,IAAApP,EACAsP,EAAAnD,EAAAC,EAAApL,EAAAuL,GACAJ,EACA,uBAAAC,EAAAqB,IAAA,EAAA2B,KAAA,EAAAtC,EAAAP,GAKA,OAAAJ,EACA,aAjHA,IAAAH,EAAA1L,EAAA,IACAyO,EAAAzO,EAAA,IACA2L,EAAA3L,EAAA,IAWA,SAAAgP,EAAAnD,EAAAC,EAAAC,EAAAE,GAEA,GAAAH,EAAAI,aAAAqC,MACA1C,EAAA,+CAAAE,EAAAE,GAAAH,EAAAqB,IAAA,EAAA,KAAA,GAAArB,EAAAqB,IAAA,EAAA,KAAA,OADA,CAIA,IAAA+B,GAAApD,EAAAqB,IAAA,EAAA,KAAA,EACArB,EAAAqD,cACAtD,EAAA,kCAAAI,EAAAJ,CACA,eAAAqD,EADArD,CAEA,cAAAI,EAFAJ,CAGA,YAEAA,EAAA,oDAAAE,EAAAE,EAAAiD,GACApD,EAAAqD,cACAtD,EAAA,+CC9BA5L,EAAAC,QAAAwL,EAGA,IAAA0D,EAAApP,EAAA,MACA0L,EAAA5G,UAAAnB,OAAA0L,OAAAD,EAAAtK,YAAAwK,YAAA5D,GAAA6D,UAAA,OAEA,IAAAC,EAAAxP,EAAA,IACA2L,EAAA3L,EAAA,IAaA,SAAA0L,EAAAoB,EAAAX,EAAAxG,EAAA8J,EAAAC,GAGA,GAFAN,EAAAlE,KAAAtG,KAAAkI,EAAAnH,GAEAwG,GAAA,iBAAAA,EACA,MAAAwD,UAAA,4BAoCA,GA9BA/K,KAAA+I,WAAA,GAMA/I,KAAAuH,OAAAxI,OAAA0L,OAAAzK,KAAA+I,YAMA/I,KAAA6K,QAAAA,EAMA7K,KAAA8K,SAAAA,GAAA,GAMA9K,KAAAgL,SAAAlQ,EAMAyM,EACA,IAAA,IAAAvI,EAAAD,OAAAC,KAAAuI,GAAAzK,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACA,iBAAAyK,EAAAvI,EAAAlC,MACAkD,KAAA+I,WAAA/I,KAAAuH,OAAAvI,EAAAlC,IAAAyK,EAAAvI,EAAAlC,KAAAkC,EAAAlC,IAiBAgK,EAAAmE,SAAA,SAAA/C,EAAAgD,GACA,IAAAC,EAAA,IAAArE,EAAAoB,EAAAgD,EAAA3D,OAAA2D,EAAAnK,QAAAmK,EAAAL,QAAAK,EAAAJ,UAEA,OADAK,EAAAH,SAAAE,EAAAF,SACAG,GAQArE,EAAA5G,UAAAkL,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAvE,EAAAyB,SAAA,CACA,UAAAxI,KAAAe,QACA,SAAAf,KAAAuH,OACA,WAAAvH,KAAAgL,UAAAhL,KAAAgL,SAAApP,OAAAoE,KAAAgL,SAAAlQ,EACA,UAAAwQ,EAAAtL,KAAA6K,QAAA/P,EACA,WAAAwQ,EAAAtL,KAAA8K,SAAAhQ,KAaAgM,EAAA5G,UAAAqL,IAAA,SAAArD,EAAAK,EAAAsC,GAGA,IAAA9D,EAAAyE,SAAAtD,GACA,MAAA6C,UAAA,yBAEA,IAAAhE,EAAA0E,UAAAlD,GACA,MAAAwC,UAAA,yBAEA,GAAA/K,KAAAuH,OAAAW,KAAApN,EACA,MAAAmD,MAAA,mBAAAiK,EAAA,QAAAlI,MAEA,GAAAA,KAAA0L,aAAAnD,GACA,MAAAtK,MAAA,MAAAsK,EAAA,mBAAAvI,MAEA,GAAAA,KAAA2L,eAAAzD,GACA,MAAAjK,MAAA,SAAAiK,EAAA,oBAAAlI,MAEA,GAAAA,KAAA+I,WAAAR,KAAAzN,EAAA,CACA,IAAAkF,KAAAe,UAAAf,KAAAe,QAAA6K,YACA,MAAA3N,MAAA,gBAAAsK,EAAA,OAAAvI,MACAA,KAAAuH,OAAAW,GAAAK,OAEAvI,KAAA+I,WAAA/I,KAAAuH,OAAAW,GAAAK,GAAAL,EAGA,OADAlI,KAAA8K,SAAA5C,GAAA2C,GAAA,KACA7K,MAUA8G,EAAA5G,UAAA2L,OAAA,SAAA3D,GAEA,IAAAnB,EAAAyE,SAAAtD,GACA,MAAA6C,UAAA,yBAEA,IAAAzI,EAAAtC,KAAAuH,OAAAW,GACA,GAAA,MAAA5F,EACA,MAAArE,MAAA,SAAAiK,EAAA,uBAAAlI,MAMA,cAJAA,KAAA+I,WAAAzG,UACAtC,KAAAuH,OAAAW,UACAlI,KAAA8K,SAAA5C,GAEAlI,MAQA8G,EAAA5G,UAAAwL,aAAA,SAAAnD,GACA,OAAAqC,EAAAc,aAAA1L,KAAAgL,SAAAzC,IAQAzB,EAAA5G,UAAAyL,eAAA,SAAAzD,GACA,OAAA0C,EAAAe,eAAA3L,KAAAgL,SAAA9C,4CClLA7M,EAAAC,QAAAwQ,EAGA,IAAAtB,EAAApP,EAAA,MACA0Q,EAAA5L,UAAAnB,OAAA0L,OAAAD,EAAAtK,YAAAwK,YAAAoB,GAAAnB,UAAA,QAEA,IAIAoB,EAJAjF,EAAA1L,EAAA,IACAyO,EAAAzO,EAAA,IACA2L,EAAA3L,EAAA,IAIA4Q,EAAA,+BAyCA,SAAAF,EAAA5D,EAAAK,EAAAX,EAAAqE,EAAAC,EAAAnL,EAAA8J,GAcA,GAZA9D,EAAAoF,SAAAF,IACApB,EAAAqB,EACAnL,EAAAkL,EACAA,EAAAC,EAAApR,GACAiM,EAAAoF,SAAAD,KACArB,EAAA9J,EACAA,EAAAmL,EACAA,EAAApR,GAGA0P,EAAAlE,KAAAtG,KAAAkI,EAAAnH,IAEAgG,EAAA0E,UAAAlD,IAAAA,EAAA,EACA,MAAAwC,UAAA,qCAEA,IAAAhE,EAAAyE,SAAA5D,GACA,MAAAmD,UAAA,yBAEA,GAAAkB,IAAAnR,IAAAkR,EAAA9N,KAAA+N,EAAAA,EAAAvN,WAAA0N,eACA,MAAArB,UAAA,8BAEA,GAAAmB,IAAApR,IAAAiM,EAAAyE,SAAAU,GACA,MAAAnB,UAAA,2BAMA/K,KAAAiM,KAAAA,GAAA,aAAAA,EAAAA,EAAAnR,EAMAkF,KAAA4H,KAAAA,EAMA5H,KAAAuI,GAAAA,EAMAvI,KAAAkM,OAAAA,GAAApR,EAMAkF,KAAAiK,SAAA,aAAAgC,EAMAjM,KAAAqK,UAAArK,KAAAiK,SAMAjK,KAAAwH,SAAA,aAAAyE,EAMAjM,KAAAoI,KAAA,EAMApI,KAAAqM,QAAA,KAMArM,KAAA8I,OAAA,KAMA9I,KAAAyH,YAAA,KAMAzH,KAAAsM,aAAA,KAMAtM,KAAAgJ,OAAAjC,EAAAwF,MAAA1C,EAAAb,KAAApB,KAAA9M,EAMAkF,KAAAqJ,MAAA,UAAAzB,EAMA5H,KAAAsH,aAAA,KAMAtH,KAAAwM,eAAA,KAMAxM,KAAAyM,eAAA,KAOAzM,KAAA0M,EAAA,KAMA1M,KAAA6K,QAAAA,EA7JAiB,EAAAb,SAAA,SAAA/C,EAAAgD,GACA,OAAA,IAAAY,EAAA5D,EAAAgD,EAAA3C,GAAA2C,EAAAtD,KAAAsD,EAAAe,KAAAf,EAAAgB,OAAAhB,EAAAnK,QAAAmK,EAAAL,UAqKA9L,OAAA4N,eAAAb,EAAA5L,UAAA,SAAA,CACA0M,IAAA,WAIA,OAFA,OAAA5M,KAAA0M,IACA1M,KAAA0M,GAAA,IAAA1M,KAAA6M,UAAA,WACA7M,KAAA0M,KAOAZ,EAAA5L,UAAA4M,UAAA,SAAA5E,EAAAxI,EAAAqN,GAGA,MAFA,WAAA7E,IACAlI,KAAA0M,EAAA,MACAlC,EAAAtK,UAAA4M,UAAAxG,KAAAtG,KAAAkI,EAAAxI,EAAAqN,IAwBAjB,EAAA5L,UAAAkL,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAvE,EAAAyB,SAAA,CACA,OAAA,aAAAxI,KAAAiM,MAAAjM,KAAAiM,MAAAnR,EACA,OAAAkF,KAAA4H,KACA,KAAA5H,KAAAuI,GACA,SAAAvI,KAAAkM,OACA,UAAAlM,KAAAe,QACA,UAAAuK,EAAAtL,KAAA6K,QAAA/P,KASAgR,EAAA5L,UAAAjE,QAAA,WAEA,GAAA+D,KAAAgN,SACA,OAAAhN,KA0BA,IAxBAA,KAAAyH,YAAAoC,EAAAoD,SAAAjN,KAAA4H,SAAA9M,IACAkF,KAAAsH,cAAAtH,KAAAyM,eAAAzM,KAAAyM,eAAAS,OAAAlN,KAAAkN,QAAAC,iBAAAnN,KAAA4H,MACA5H,KAAAsH,wBAAAyE,EACA/L,KAAAyH,YAAA,KAEAzH,KAAAyH,YAAAzH,KAAAsH,aAAAC,OAAAxI,OAAAC,KAAAgB,KAAAsH,aAAAC,QAAA,KAIAvH,KAAAe,SAAA,MAAAf,KAAAe,QAAA,UACAf,KAAAyH,YAAAzH,KAAAe,QAAA,QACAf,KAAAsH,wBAAAR,GAAA,iBAAA9G,KAAAyH,cACAzH,KAAAyH,YAAAzH,KAAAsH,aAAAC,OAAAvH,KAAAyH,eAIAzH,KAAAe,WACA,IAAAf,KAAAe,QAAAgJ,SAAA/J,KAAAe,QAAAgJ,SAAAjP,IAAAkF,KAAAsH,cAAAtH,KAAAsH,wBAAAR,WACA9G,KAAAe,QAAAgJ,OACAhL,OAAAC,KAAAgB,KAAAe,SAAAnF,SACAoE,KAAAe,QAAAjG,IAIAkF,KAAAgJ,KACAhJ,KAAAyH,YAAAV,EAAAwF,KAAAa,WAAApN,KAAAyH,YAAA,MAAAzH,KAAA4H,KAAAnL,OAAA,IAGAsC,OAAAsO,QACAtO,OAAAsO,OAAArN,KAAAyH,kBAEA,GAAAzH,KAAAqJ,OAAA,iBAAArJ,KAAAyH,YAAA,CACA,IAAAlF,EACAwE,EAAA1K,OAAA6B,KAAA8B,KAAAyH,aACAV,EAAA1K,OAAAyB,OAAAkC,KAAAyH,YAAAlF,EAAAwE,EAAAuG,UAAAvG,EAAA1K,OAAAT,OAAAoE,KAAAyH,cAAA,GAEAV,EAAAR,KAAAG,MAAA1G,KAAAyH,YAAAlF,EAAAwE,EAAAuG,UAAAvG,EAAAR,KAAA3K,OAAAoE,KAAAyH,cAAA,GACAzH,KAAAyH,YAAAlF,EAeA,OAXAvC,KAAAoI,IACApI,KAAAsM,aAAAvF,EAAAwG,YACAvN,KAAAwH,SACAxH,KAAAsM,aAAAvF,EAAAyG,WAEAxN,KAAAsM,aAAAtM,KAAAyH,YAGAzH,KAAAkN,kBAAAnB,IACA/L,KAAAkN,OAAAO,KAAAvN,UAAAF,KAAAkI,MAAAlI,KAAAsM,cAEA9B,EAAAtK,UAAAjE,QAAAqK,KAAAtG,OAGA8L,EAAA5L,UAAAoI,WAAA,WACA,QAAAtI,KAAA6M,UAAA,qBAGAf,EAAA5L,UAAAqK,WAAA,WACA,QAAAvK,KAAA6M,UAAA,oBAuBAf,EAAA4B,EAAA,SAAAC,EAAAC,EAAAC,EAAAvB,GAUA,MAPA,mBAAAsB,EACAA,EAAA7G,EAAA+G,aAAAF,GAAA1F,KAGA0F,GAAA,iBAAAA,IACAA,EAAA7G,EAAAgH,aAAAH,GAAA1F,MAEA,SAAAhI,EAAA8N,GACAjH,EAAA+G,aAAA5N,EAAAwK,aACAa,IAAA,IAAAO,EAAAkC,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAA3B,OAkBAR,EAAAoC,EAAA,SAAAC,GACApC,EAAAoC,iDCxXA,IAAAjT,EAAAG,EAAAC,QAAAF,EAAA,IAEAF,EAAAkT,MAAA,QAoDAlT,EAAAmT,KAjCA,SAAAvN,EAAAwN,EAAAtN,GAMA,MALA,mBAAAsN,GACAtN,EAAAsN,EACAA,EAAA,IAAApT,EAAAqT,MACAD,IACAA,EAAA,IAAApT,EAAAqT,MACAD,EAAAD,KAAAvN,EAAAE,IA2CA9F,EAAAsT,SANA,SAAA1N,EAAAwN,GAGA,OAFAA,IACAA,EAAA,IAAApT,EAAAqT,MACAD,EAAAE,SAAA1N,IAMA5F,EAAAuT,QAAArT,EAAA,IACAF,EAAAwT,QAAAtT,EAAA,IACAF,EAAAyT,SAAAvT,EAAA,IACAF,EAAA2L,UAAAzL,EAAA,IAGAF,EAAAsP,iBAAApP,EAAA,IACAF,EAAA0P,UAAAxP,EAAA,IACAF,EAAAqT,KAAAnT,EAAA,IACAF,EAAA4L,KAAA1L,EAAA,IACAF,EAAA6Q,KAAA3Q,EAAA,IACAF,EAAA4Q,MAAA1Q,EAAA,IACAF,EAAA0T,MAAAxT,EAAA,IACAF,EAAA2T,SAAAzT,EAAA,IACAF,EAAA4T,QAAA1T,EAAA,IACAF,EAAA6T,OAAA3T,EAAA,IAGAF,EAAA8T,QAAA5T,EAAA,IACAF,EAAA+T,SAAA7T,EAAA,IAGAF,EAAA2O,MAAAzO,EAAA,IACAF,EAAA6L,KAAA3L,EAAA,IAGAF,EAAAsP,iBAAA0D,EAAAhT,EAAAqT,MACArT,EAAA0P,UAAAsD,EAAAhT,EAAA6Q,KAAA7Q,EAAA4T,QAAA5T,EAAA4L,MACA5L,EAAAqT,KAAAL,EAAAhT,EAAA6Q,MACA7Q,EAAA4Q,MAAAoC,EAAAhT,EAAA6Q,gJCtGA,IAAA7Q,EAAAI,EA2BA,SAAA4T,IACAhU,EAAAiU,OAAAjB,EAAAhT,EAAAkU,cACAlU,EAAA6L,KAAAmH,IArBAhT,EAAAkT,MAAA,UAGAlT,EAAAmU,OAAAjU,EAAA,IACAF,EAAAoU,aAAAlU,EAAA,IACAF,EAAAiU,OAAA/T,EAAA,IACAF,EAAAkU,aAAAhU,EAAA,IAGAF,EAAA6L,KAAA3L,EAAA,IACAF,EAAAqU,IAAAnU,EAAA,IACAF,EAAAsU,MAAApU,EAAA,IACAF,EAAAgU,UAAAA,EAaAhU,EAAAmU,OAAAnB,EAAAhT,EAAAoU,cACAJ,oEClCA7T,EAAAC,QAAAuT,EAGA,IAAA/C,EAAA1Q,EAAA,MACAyT,EAAA3O,UAAAnB,OAAA0L,OAAAqB,EAAA5L,YAAAwK,YAAAmE,GAAAlE,UAAA,WAEA,IAAAd,EAAAzO,EAAA,IACA2L,EAAA3L,EAAA,IAcA,SAAAyT,EAAA3G,EAAAK,EAAAqB,EAAAhC,EAAA7G,EAAA8J,GAIA,GAHAiB,EAAAxF,KAAAtG,KAAAkI,EAAAK,EAAAX,EAAA9M,EAAAA,EAAAiG,EAAA8J,IAGA9D,EAAAyE,SAAA5B,GACA,MAAAmB,UAAA,4BAMA/K,KAAA4J,QAAAA,EAMA5J,KAAAyP,gBAAA,KAGAzP,KAAAoI,KAAA,EAwBAyG,EAAA5D,SAAA,SAAA/C,EAAAgD,GACA,OAAA,IAAA2D,EAAA3G,EAAAgD,EAAA3C,GAAA2C,EAAAtB,QAAAsB,EAAAtD,KAAAsD,EAAAnK,QAAAmK,EAAAL,UAQAgE,EAAA3O,UAAAkL,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAvE,EAAAyB,SAAA,CACA,UAAAxI,KAAA4J,QACA,OAAA5J,KAAA4H,KACA,KAAA5H,KAAAuI,GACA,SAAAvI,KAAAkM,OACA,UAAAlM,KAAAe,QACA,UAAAuK,EAAAtL,KAAA6K,QAAA/P,KAOA+T,EAAA3O,UAAAjE,QAAA,WACA,GAAA+D,KAAAgN,SACA,OAAAhN,KAGA,GAAA6J,EAAAM,OAAAnK,KAAA4J,WAAA9O,EACA,MAAAmD,MAAA,qBAAA+B,KAAA4J,SAEA,OAAAkC,EAAA5L,UAAAjE,QAAAqK,KAAAtG,OAaA6O,EAAAnB,EAAA,SAAAC,EAAA+B,EAAAC,GAUA,MAPA,mBAAAA,EACAA,EAAA5I,EAAA+G,aAAA6B,GAAAzH,KAGAyH,GAAA,iBAAAA,IACAA,EAAA5I,EAAAgH,aAAA4B,GAAAzH,MAEA,SAAAhI,EAAA8N,GACAjH,EAAA+G,aAAA5N,EAAAwK,aACAa,IAAA,IAAAsD,EAAAb,EAAAL,EAAA+B,EAAAC,8CC1HAtU,EAAAC,QAAA0T,EAEA,IAAAjI,EAAA3L,EAAA,IASA,SAAA4T,EAAAY,GAEA,GAAAA,EACA,IAAA,IAAA5Q,EAAAD,OAAAC,KAAA4Q,GAAA9S,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAkD,KAAAhB,EAAAlC,IAAA8S,EAAA5Q,EAAAlC,IA0BAkS,EAAAvE,OAAA,SAAAmF,GACA,OAAA5P,KAAA6P,MAAApF,OAAAmF,IAWAZ,EAAAjS,OAAA,SAAAsP,EAAAyD,GACA,OAAA9P,KAAA6P,MAAA9S,OAAAsP,EAAAyD,IAWAd,EAAAe,gBAAA,SAAA1D,EAAAyD,GACA,OAAA9P,KAAA6P,MAAAE,gBAAA1D,EAAAyD,IAYAd,EAAAlR,OAAA,SAAAkS,GACA,OAAAhQ,KAAA6P,MAAA/R,OAAAkS,IAYAhB,EAAAiB,gBAAA,SAAAD,GACA,OAAAhQ,KAAA6P,MAAAI,gBAAAD,IAUAhB,EAAAkB,OAAA,SAAA7D,GACA,OAAArM,KAAA6P,MAAAK,OAAA7D,IAUA2C,EAAAlH,WAAA,SAAAqI,GACA,OAAAnQ,KAAA6P,MAAA/H,WAAAqI,IAWAnB,EAAAxG,SAAA,SAAA6D,EAAAtL,GACA,OAAAf,KAAA6P,MAAArH,SAAA6D,EAAAtL,IAOAiO,EAAA9O,UAAAkL,OAAA,WACA,OAAApL,KAAA6P,MAAArH,SAAAxI,KAAA+G,EAAAsE,4CCtIAhQ,EAAAC,QAAAyT,EAGA,IAAAvE,EAAApP,EAAA,MACA2T,EAAA7O,UAAAnB,OAAA0L,OAAAD,EAAAtK,YAAAwK,YAAAqE,GAAApE,UAAA,SAEA,IAAA5D,EAAA3L,EAAA,IAgBA,SAAA2T,EAAA7G,EAAAN,EAAAwI,EAAAvO,EAAAwO,EAAAC,EAAAvP,EAAA8J,GAYA,GATA9D,EAAAoF,SAAAkE,IACAtP,EAAAsP,EACAA,EAAAC,EAAAxV,GACAiM,EAAAoF,SAAAmE,KACAvP,EAAAuP,EACAA,EAAAxV,GAIA8M,IAAA9M,IAAAiM,EAAAyE,SAAA5D,GACA,MAAAmD,UAAA,yBAGA,IAAAhE,EAAAyE,SAAA4E,GACA,MAAArF,UAAA,gCAGA,IAAAhE,EAAAyE,SAAA3J,GACA,MAAAkJ,UAAA,iCAEAP,EAAAlE,KAAAtG,KAAAkI,EAAAnH,GAMAf,KAAA4H,KAAAA,GAAA,MAMA5H,KAAAoQ,YAAAA,EAMApQ,KAAAqQ,gBAAAA,GAAAvV,EAMAkF,KAAA6B,aAAAA,EAMA7B,KAAAsQ,iBAAAA,GAAAxV,EAMAkF,KAAAuQ,oBAAA,KAMAvQ,KAAAwQ,qBAAA,KAMAxQ,KAAA6K,QAAAA,EAqBAkE,EAAA9D,SAAA,SAAA/C,EAAAgD,GACA,OAAA,IAAA6D,EAAA7G,EAAAgD,EAAAtD,KAAAsD,EAAAkF,YAAAlF,EAAArJ,aAAAqJ,EAAAmF,cAAAnF,EAAAoF,eAAApF,EAAAnK,QAAAmK,EAAAL,UAQAkE,EAAA7O,UAAAkL,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAvE,EAAAyB,SAAA,CACA,OAAA,QAAAxI,KAAA4H,MAAA5H,KAAA4H,MAAA9M,EACA,cAAAkF,KAAAoQ,YACA,gBAAApQ,KAAAqQ,cACA,eAAArQ,KAAA6B,aACA,iBAAA7B,KAAAsQ,eACA,UAAAtQ,KAAAe,QACA,UAAAuK,EAAAtL,KAAA6K,QAAA/P,KAOAiU,EAAA7O,UAAAjE,QAAA,WAGA,OAAA+D,KAAAgN,SACAhN,MAEAA,KAAAuQ,oBAAAvQ,KAAAkN,OAAAuD,WAAAzQ,KAAAoQ,aACApQ,KAAAwQ,qBAAAxQ,KAAAkN,OAAAuD,WAAAzQ,KAAA6B,cAEA2I,EAAAtK,UAAAjE,QAAAqK,KAAAtG,0CCpJA3E,EAAAC,QAAAsP,EAGA,IAAAJ,EAAApP,EAAA,MACAwP,EAAA1K,UAAAnB,OAAA0L,OAAAD,EAAAtK,YAAAwK,YAAAE,GAAAD,UAAA,YAEA,IAGAoB,EACA+C,EACAhI,EALAgF,EAAA1Q,EAAA,IACA2L,EAAA3L,EAAA,IAoCA,SAAAsV,EAAAC,EAAAtF,GACA,IAAAsF,IAAAA,EAAA/U,OACA,OAAAd,EAEA,IADA,IAAA8V,EAAA,GACA9T,EAAA,EAAAA,EAAA6T,EAAA/U,SAAAkB,EACA8T,EAAAD,EAAA7T,GAAAoL,MAAAyI,EAAA7T,GAAAsO,OAAAC,GACA,OAAAuF,EA4CA,SAAAhG,EAAA1C,EAAAnH,GACAyJ,EAAAlE,KAAAtG,KAAAkI,EAAAnH,GAMAf,KAAA6Q,OAAA/V,EAOAkF,KAAA8Q,EAAA,KAGA,SAAAC,EAAAC,GAEA,OADAA,EAAAF,EAAA,KACAE,EAhFApG,EAAAK,SAAA,SAAA/C,EAAAgD,GACA,OAAA,IAAAN,EAAA1C,EAAAgD,EAAAnK,SAAAkQ,QAAA/F,EAAA2F,SAmBAjG,EAAA8F,YAAAA,EAQA9F,EAAAc,aAAA,SAAAV,EAAAzC,GACA,GAAAyC,EACA,IAAA,IAAAlO,EAAA,EAAAA,EAAAkO,EAAApP,SAAAkB,EACA,GAAA,iBAAAkO,EAAAlO,IAAAkO,EAAAlO,GAAA,IAAAyL,GAAAyC,EAAAlO,GAAA,GAAAyL,EACA,OAAA,EACA,OAAA,GASAqC,EAAAe,eAAA,SAAAX,EAAA9C,GACA,GAAA8C,EACA,IAAA,IAAAlO,EAAA,EAAAA,EAAAkO,EAAApP,SAAAkB,EACA,GAAAkO,EAAAlO,KAAAoL,EACA,OAAA,EACA,OAAA,GA0CAnJ,OAAA4N,eAAA/B,EAAA1K,UAAA,cAAA,CACA0M,IAAA,WACA,OAAA5M,KAAA8Q,IAAA9Q,KAAA8Q,EAAA/J,EAAAmK,QAAAlR,KAAA6Q,YA6BAjG,EAAA1K,UAAAkL,OAAA,SAAAC,GACA,OAAAtE,EAAAyB,SAAA,CACA,UAAAxI,KAAAe,QACA,SAAA2P,EAAA1Q,KAAAmR,YAAA9F,MASAT,EAAA1K,UAAA+Q,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAAP,EAAAQ,EAAAtS,OAAAC,KAAAoS,GAAAtU,EAAA,EAAAA,EAAAuU,EAAAzV,SAAAkB,EACA+T,EAAAO,EAAAC,EAAAvU,IAJAkD,KAKAuL,KACAsF,EAAA7I,SAAAlN,EACAiR,EAAAd,SACA4F,EAAAtJ,SAAAzM,EACAgM,EAAAmE,SACA4F,EAAAS,UAAAxW,EACAgU,EAAA7D,SACA4F,EAAAtI,KAAAzN,EACAgR,EAAAb,SACAL,EAAAK,UAAAoG,EAAAvU,GAAA+T,IAIA,OAAA7Q,MAQA4K,EAAA1K,UAAA0M,IAAA,SAAA1E,GACA,OAAAlI,KAAA6Q,QAAA7Q,KAAA6Q,OAAA3I,IACA,MAUA0C,EAAA1K,UAAAqR,QAAA,SAAArJ,GACA,GAAAlI,KAAA6Q,QAAA7Q,KAAA6Q,OAAA3I,aAAApB,EACA,OAAA9G,KAAA6Q,OAAA3I,GAAAX,OACA,MAAAtJ,MAAA,iBAAAiK,IAUA0C,EAAA1K,UAAAqL,IAAA,SAAA4E,GAEA,KAAAA,aAAArE,GAAAqE,EAAAjE,SAAApR,GAAAqV,aAAApE,GAAAoE,aAAArJ,GAAAqJ,aAAArB,GAAAqB,aAAAvF,GACA,MAAAG,UAAA,wCAEA,GAAA/K,KAAA6Q,OAEA,CACA,IAAAW,EAAAxR,KAAA4M,IAAAuD,EAAAjI,MACA,GAAAsJ,EAAA,CACA,KAAAA,aAAA5G,GAAAuF,aAAAvF,IAAA4G,aAAAzF,GAAAyF,aAAA1C,EAWA,MAAA7Q,MAAA,mBAAAkS,EAAAjI,KAAA,QAAAlI,MARA,IADA,IAAA6Q,EAAAW,EAAAL,YACArU,EAAA,EAAAA,EAAA+T,EAAAjV,SAAAkB,EACAqT,EAAA5E,IAAAsF,EAAA/T,IACAkD,KAAA6L,OAAA2F,GACAxR,KAAA6Q,SACA7Q,KAAA6Q,OAAA,IACAV,EAAAsB,WAAAD,EAAAzQ,SAAA,SAZAf,KAAA6Q,OAAA,GAoBA,OAFA7Q,KAAA6Q,OAAAV,EAAAjI,MAAAiI,GACAuB,MAAA1R,MACA+Q,EAAA/Q,OAUA4K,EAAA1K,UAAA2L,OAAA,SAAAsE,GAEA,KAAAA,aAAA3F,GACA,MAAAO,UAAA,qCACA,GAAAoF,EAAAjD,SAAAlN,KACA,MAAA/B,MAAAkS,EAAA,uBAAAnQ,MAOA,cALAA,KAAA6Q,OAAAV,EAAAjI,MACAnJ,OAAAC,KAAAgB,KAAA6Q,QAAAjV,SACAoE,KAAA6Q,OAAA/V,GAEAqV,EAAAwB,SAAA3R,MACA+Q,EAAA/Q,OASA4K,EAAA1K,UAAA0R,OAAA,SAAArM,EAAA2F,GAEA,GAAAnE,EAAAyE,SAAAjG,GACAA,EAAAA,EAAAG,MAAA,UACA,IAAAhK,MAAAmW,QAAAtM,GACA,MAAAwF,UAAA,gBACA,GAAAxF,GAAAA,EAAA3J,QAAA,KAAA2J,EAAA,GACA,MAAAtH,MAAA,yBAGA,IADA,IAAA6T,EAAA9R,KACA,EAAAuF,EAAA3J,QAAA,CACA,IAAAmW,EAAAxM,EAAAM,QACA,GAAAiM,EAAAjB,QAAAiB,EAAAjB,OAAAkB,IAEA,MADAD,EAAAA,EAAAjB,OAAAkB,cACAnH,GACA,MAAA3M,MAAA,kDAEA6T,EAAAvG,IAAAuG,EAAA,IAAAlH,EAAAmH,IAIA,OAFA7G,GACA4G,EAAAb,QAAA/F,GACA4G,GAOAlH,EAAA1K,UAAA8R,WAAA,WAEA,IADA,IAAAnB,EAAA7Q,KAAAmR,YAAArU,EAAA,EACAA,EAAA+T,EAAAjV,QACAiV,EAAA/T,aAAA8N,EACAiG,EAAA/T,KAAAkV,aAEAnB,EAAA/T,KAAAb,UACA,OAAA+D,KAAA/D,WAUA2O,EAAA1K,UAAA+R,OAAA,SAAA1M,EAAA2M,EAAAC,GASA,GANA,kBAAAD,GACAC,EAAAD,EACAA,EAAApX,GACAoX,IAAAxW,MAAAmW,QAAAK,KACAA,EAAA,CAAAA,IAEAnL,EAAAyE,SAAAjG,IAAAA,EAAA3J,OAAA,CACA,GAAA,MAAA2J,EACA,OAAAvF,KAAAsO,KACA/I,EAAAA,EAAAG,MAAA,UACA,IAAAH,EAAA3J,OACA,OAAAoE,KAGA,GAAA,KAAAuF,EAAA,GACA,OAAAvF,KAAAsO,KAAA2D,OAAA1M,EAAA5H,MAAA,GAAAuU,GAGA,IAAAE,EAAApS,KAAA4M,IAAArH,EAAA,IACA,GAAA6M,GACA,GAAA,IAAA7M,EAAA3J,QACA,IAAAsW,IAAA,EAAAA,EAAAzI,QAAA2I,EAAA1H,aACA,OAAA0H,OACA,GAAAA,aAAAxH,IAAAwH,EAAAA,EAAAH,OAAA1M,EAAA5H,MAAA,GAAAuU,GAAA,IACA,OAAAE,OAIA,IAAA,IAAAtV,EAAA,EAAAA,EAAAkD,KAAAmR,YAAAvV,SAAAkB,EACA,GAAAkD,KAAA8Q,EAAAhU,aAAA8N,IAAAwH,EAAApS,KAAA8Q,EAAAhU,GAAAmV,OAAA1M,EAAA2M,GAAA,IACA,OAAAE,EAGA,OAAA,OAAApS,KAAAkN,QAAAiF,EACA,KACAnS,KAAAkN,OAAA+E,OAAA1M,EAAA2M,IAqBAtH,EAAA1K,UAAAuQ,WAAA,SAAAlL,GACA,IAAA6M,EAAApS,KAAAiS,OAAA1M,EAAA,CAAAwG,IACA,IAAAqG,EACA,MAAAnU,MAAA,iBAAAsH,GACA,OAAA6M,GAUAxH,EAAA1K,UAAAmS,WAAA,SAAA9M,GACA,IAAA6M,EAAApS,KAAAiS,OAAA1M,EAAA,CAAAuB,IACA,IAAAsL,EACA,MAAAnU,MAAA,iBAAAsH,EAAA,QAAAvF,MACA,OAAAoS,GAUAxH,EAAA1K,UAAAiN,iBAAA,SAAA5H,GACA,IAAA6M,EAAApS,KAAAiS,OAAA1M,EAAA,CAAAwG,EAAAjF,IACA,IAAAsL,EACA,MAAAnU,MAAA,yBAAAsH,EAAA,QAAAvF,MACA,OAAAoS,GAUAxH,EAAA1K,UAAAoS,cAAA,SAAA/M,GACA,IAAA6M,EAAApS,KAAAiS,OAAA1M,EAAA,CAAAuJ,IACA,IAAAsD,EACA,MAAAnU,MAAA,oBAAAsH,EAAA,QAAAvF,MACA,OAAAoS,GAIAxH,EAAAsD,EAAA,SAAAC,EAAAoE,EAAAC,GACAzG,EAAAoC,EACAW,EAAAyD,EACAzL,EAAA0L,4CC9aAnX,EAAAC,QAAAkP,GAEAG,UAAA,mBAEA,IAEA4D,EAFAxH,EAAA3L,EAAA,IAYA,SAAAoP,EAAAtC,EAAAnH,GAEA,IAAAgG,EAAAyE,SAAAtD,GACA,MAAA6C,UAAA,yBAEA,GAAAhK,IAAAgG,EAAAoF,SAAApL,GACA,MAAAgK,UAAA,6BAMA/K,KAAAe,QAAAA,EAMAf,KAAAkI,KAAAA,EAMAlI,KAAAkN,OAAA,KAMAlN,KAAAgN,UAAA,EAMAhN,KAAA6K,QAAA,KAMA7K,KAAAc,SAAA,KAGA/B,OAAA0T,iBAAAjI,EAAAtK,UAAA,CAQAoO,KAAA,CACA1B,IAAA,WAEA,IADA,IAAAkF,EAAA9R,KACA,OAAA8R,EAAA5E,QACA4E,EAAAA,EAAA5E,OACA,OAAA4E,IAUApK,SAAA,CACAkF,IAAA,WAGA,IAFA,IAAArH,EAAA,CAAAvF,KAAAkI,MACA4J,EAAA9R,KAAAkN,OACA4E,GACAvM,EAAAmN,QAAAZ,EAAA5J,MACA4J,EAAAA,EAAA5E,OAEA,OAAA3H,EAAA3H,KAAA,SAUA4M,EAAAtK,UAAAkL,OAAA,WACA,MAAAnN,SAQAuM,EAAAtK,UAAAwR,MAAA,SAAAxE,GACAlN,KAAAkN,QAAAlN,KAAAkN,SAAAA,GACAlN,KAAAkN,OAAArB,OAAA7L,MACAA,KAAAkN,OAAAA,EACAlN,KAAAgN,UAAA,EACA,IAAAsB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAqE,EAAA3S,OAQAwK,EAAAtK,UAAAyR,SAAA,SAAAzE,GACA,IAAAoB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAsE,EAAA5S,MACAA,KAAAkN,OAAA,KACAlN,KAAAgN,UAAA,GAOAxC,EAAAtK,UAAAjE,QAAA,WACA,OAAA+D,KAAAgN,UAEAhN,KAAAsO,gBAAAC,IACAvO,KAAAgN,UAAA,GAFAhN,MAWAwK,EAAAtK,UAAA2M,UAAA,SAAA3E,GACA,OAAAlI,KAAAe,QACAf,KAAAe,QAAAmH,GACApN,GAUA0P,EAAAtK,UAAA4M,UAAA,SAAA5E,EAAAxI,EAAAqN,GAGA,OAFAA,GAAA/M,KAAAe,SAAAf,KAAAe,QAAAmH,KAAApN,KACAkF,KAAAe,UAAAf,KAAAe,QAAA,KAAAmH,GAAAxI,GACAM,MASAwK,EAAAtK,UAAAuR,WAAA,SAAA1Q,EAAAgM,GACA,GAAAhM,EACA,IAAA,IAAA/B,EAAAD,OAAAC,KAAA+B,GAAAjE,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAkD,KAAA8M,UAAA9N,EAAAlC,GAAAiE,EAAA/B,EAAAlC,IAAAiQ,GACA,OAAA/M,MAOAwK,EAAAtK,UAAAxB,SAAA,WACA,IAAAiM,EAAA3K,KAAA0K,YAAAC,UACAjD,EAAA1H,KAAA0H,SACA,OAAAA,EAAA9L,OACA+O,EAAA,IAAAjD,EACAiD,GAIAH,EAAA0D,EAAA,SAAA2E,GACAtE,EAAAsE,+BCrMAxX,EAAAC,QAAAsT,EAGA,IAAApE,EAAApP,EAAA,MACAwT,EAAA1O,UAAAnB,OAAA0L,OAAAD,EAAAtK,YAAAwK,YAAAkE,GAAAjE,UAAA,QAEA,IAAAmB,EAAA1Q,EAAA,IACA2L,EAAA3L,EAAA,IAYA,SAAAwT,EAAA1G,EAAA4K,EAAA/R,EAAA8J,GAQA,GAPAnP,MAAAmW,QAAAiB,KACA/R,EAAA+R,EACAA,EAAAhY,GAEA0P,EAAAlE,KAAAtG,KAAAkI,EAAAnH,GAGA+R,IAAAhY,IAAAY,MAAAmW,QAAAiB,GACA,MAAA/H,UAAA,+BAMA/K,KAAA+S,MAAAD,GAAA,GAOA9S,KAAAiI,YAAA,GAMAjI,KAAA6K,QAAAA,EA0CA,SAAAmI,EAAAD,GACA,GAAAA,EAAA7F,OACA,IAAA,IAAApQ,EAAA,EAAAA,EAAAiW,EAAA9K,YAAArM,SAAAkB,EACAiW,EAAA9K,YAAAnL,GAAAoQ,QACA6F,EAAA7F,OAAA3B,IAAAwH,EAAA9K,YAAAnL,IA7BA8R,EAAA3D,SAAA,SAAA/C,EAAAgD,GACA,OAAA,IAAA0D,EAAA1G,EAAAgD,EAAA6H,MAAA7H,EAAAnK,QAAAmK,EAAAL,UAQA+D,EAAA1O,UAAAkL,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAvE,EAAAyB,SAAA,CACA,UAAAxI,KAAAe,QACA,QAAAf,KAAA+S,MACA,UAAAzH,EAAAtL,KAAA6K,QAAA/P,KAuBA8T,EAAA1O,UAAAqL,IAAA,SAAArE,GAGA,KAAAA,aAAA4E,GACA,MAAAf,UAAA,yBAQA,OANA7D,EAAAgG,QAAAhG,EAAAgG,SAAAlN,KAAAkN,QACAhG,EAAAgG,OAAArB,OAAA3E,GACAlH,KAAA+S,MAAAvV,KAAA0J,EAAAgB,MACAlI,KAAAiI,YAAAzK,KAAA0J,GAEA8L,EADA9L,EAAA4B,OAAA9I,MAEAA,MAQA4O,EAAA1O,UAAA2L,OAAA,SAAA3E,GAGA,KAAAA,aAAA4E,GACA,MAAAf,UAAA,yBAEA,IAAAjP,EAAAkE,KAAAiI,YAAAwB,QAAAvC,GAGA,GAAApL,EAAA,EACA,MAAAmC,MAAAiJ,EAAA,uBAAAlH,MAUA,OARAA,KAAAiI,YAAA1H,OAAAzE,EAAA,IAIA,GAHAA,EAAAkE,KAAA+S,MAAAtJ,QAAAvC,EAAAgB,QAIAlI,KAAA+S,MAAAxS,OAAAzE,EAAA,GAEAoL,EAAA4B,OAAA,KACA9I,MAMA4O,EAAA1O,UAAAwR,MAAA,SAAAxE,GACA1C,EAAAtK,UAAAwR,MAAApL,KAAAtG,KAAAkN,GAGA,IAFA,IAEApQ,EAAA,EAAAA,EAAAkD,KAAA+S,MAAAnX,SAAAkB,EAAA,CACA,IAAAoK,EAAAgG,EAAAN,IAAA5M,KAAA+S,MAAAjW,IACAoK,IAAAA,EAAA4B,SACA5B,EAAA4B,OALA9I,MAMAiI,YAAAzK,KAAA0J,GAIA8L,EAAAhT,OAMA4O,EAAA1O,UAAAyR,SAAA,SAAAzE,GACA,IAAA,IAAAhG,EAAApK,EAAA,EAAAA,EAAAkD,KAAAiI,YAAArM,SAAAkB,GACAoK,EAAAlH,KAAAiI,YAAAnL,IAAAoQ,QACAhG,EAAAgG,OAAArB,OAAA3E,GACAsD,EAAAtK,UAAAyR,SAAArL,KAAAtG,KAAAkN,IAmBA0B,EAAAlB,EAAA,WAGA,IAFA,IAAAoF,EAAApX,MAAAC,UAAAC,QACAE,EAAA,EACAA,EAAAH,UAAAC,QACAkX,EAAAhX,GAAAH,UAAAG,KACA,OAAA,SAAAoE,EAAA+S,GACAlM,EAAA+G,aAAA5N,EAAAwK,aACAa,IAAA,IAAAqD,EAAAqE,EAAAH,IACA/T,OAAA4N,eAAAzM,EAAA+S,EAAA,CACArG,IAAA7F,EAAAmM,YAAAJ,GACAK,IAAApM,EAAAqM,YAAAN,+CCtMAzX,EAAAC,QAAA6T,EAEA,IAEAC,EAFArI,EAAA3L,EAAA,IAIAiY,EAAAtM,EAAAsM,SACA9M,EAAAQ,EAAAR,KAGA,SAAA+M,EAAAtD,EAAAuD,GACA,OAAAC,WAAA,uBAAAxD,EAAAxN,IAAA,OAAA+Q,GAAA,GAAA,MAAAvD,EAAAxJ,KASA,SAAA2I,EAAAnS,GAMAgD,KAAAuC,IAAAvF,EAMAgD,KAAAwC,IAAA,EAMAxC,KAAAwG,IAAAxJ,EAAApB,OAGA,IAwCA8D,EAxCA+T,EAAA,oBAAA9R,WACA,SAAA3E,GACA,GAAAA,aAAA2E,YAAAjG,MAAAmW,QAAA7U,GACA,OAAA,IAAAmS,EAAAnS,GACA,MAAAiB,MAAA,mBAGA,SAAAjB,GACA,GAAAtB,MAAAmW,QAAA7U,GACA,OAAA,IAAAmS,EAAAnS,GACA,MAAAiB,MAAA,mBAkEA,SAAAyV,IAEA,IAAAC,EAAA,IAAAN,EAAA,EAAA,GACAvW,EAAA,EACA,KAAA,EAAAkD,KAAAwG,IAAAxG,KAAAwC,KAaA,CACA,KAAA1F,EAAA,IAAAA,EAAA,CAEA,GAAAkD,KAAAwC,KAAAxC,KAAAwG,IACA,MAAA8M,EAAAtT,MAGA,GADA2T,EAAA1O,IAAA0O,EAAA1O,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmR,EAIA,OADAA,EAAA1O,IAAA0O,EAAA1O,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,SAAA,EAAA1F,KAAA,EACA6W,EAxBA,KAAA7W,EAAA,IAAAA,EAGA,GADA6W,EAAA1O,IAAA0O,EAAA1O,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmR,EAKA,GAFAA,EAAA1O,IAAA0O,EAAA1O,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EACAmR,EAAAzO,IAAAyO,EAAAzO,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,KAAA,EACAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmR,EAgBA,GAfA7W,EAAA,EAeA,EAAAkD,KAAAwG,IAAAxG,KAAAwC,KACA,KAAA1F,EAAA,IAAAA,EAGA,GADA6W,EAAAzO,IAAAyO,EAAAzO,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,EAAA,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmR,OAGA,KAAA7W,EAAA,IAAAA,EAAA,CAEA,GAAAkD,KAAAwC,KAAAxC,KAAAwG,IACA,MAAA8M,EAAAtT,MAGA,GADA2T,EAAAzO,IAAAyO,EAAAzO,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,EAAA,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmR,EAIA,MAAA1V,MAAA,2BAkCA,SAAA2V,EAAArR,EAAArF,GACA,OAAAqF,EAAArF,EAAA,GACAqF,EAAArF,EAAA,IAAA,EACAqF,EAAArF,EAAA,IAAA,GACAqF,EAAArF,EAAA,IAAA,MAAA,EA+BA,SAAA2W,IAGA,GAAA7T,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8M,EAAAtT,KAAA,GAEA,OAAA,IAAAqT,EAAAO,EAAA5T,KAAAuC,IAAAvC,KAAAwC,KAAA,GAAAoR,EAAA5T,KAAAuC,IAAAvC,KAAAwC,KAAA,IArLA2M,EAAA1E,OAAA1D,EAAA+M,OACA,SAAA9W,GACA,OAAAmS,EAAA1E,OAAA,SAAAzN,GACA,OAAA+J,EAAA+M,OAAAC,SAAA/W,GACA,IAAAoS,EAAApS,GAEAyW,EAAAzW,KACAA,IAGAyW,EAEAtE,EAAAjP,UAAA8T,EAAAjN,EAAArL,MAAAwE,UAAA+T,UAAAlN,EAAArL,MAAAwE,UAAAvC,MAOAwR,EAAAjP,UAAAgU,QACAxU,EAAA,WACA,WACA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,QAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,KAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,GAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EAGA,IAAAM,KAAAwC,KAAA,GAAAxC,KAAAwG,IAEA,MADAxG,KAAAwC,IAAAxC,KAAAwG,IACA8M,EAAAtT,KAAA,IAEA,OAAAN,IAQAyP,EAAAjP,UAAAiU,MAAA,WACA,OAAA,EAAAnU,KAAAkU,UAOA/E,EAAAjP,UAAAkU,OAAA,WACA,IAAA1U,EAAAM,KAAAkU,SACA,OAAAxU,IAAA,IAAA,EAAAA,GAAA,GAqFAyP,EAAAjP,UAAAmU,KAAA,WACA,OAAA,IAAArU,KAAAkU,UAcA/E,EAAAjP,UAAAoU,QAAA,WAGA,GAAAtU,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8M,EAAAtT,KAAA,GAEA,OAAA4T,EAAA5T,KAAAuC,IAAAvC,KAAAwC,KAAA,IAOA2M,EAAAjP,UAAAqU,SAAA,WAGA,GAAAvU,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8M,EAAAtT,KAAA,GAEA,OAAA,EAAA4T,EAAA5T,KAAAuC,IAAAvC,KAAAwC,KAAA,IAmCA2M,EAAAjP,UAAAsU,MAAA,WAGA,GAAAxU,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8M,EAAAtT,KAAA,GAEA,IAAAN,EAAAqH,EAAAyN,MAAA1R,YAAA9C,KAAAuC,IAAAvC,KAAAwC,KAEA,OADAxC,KAAAwC,KAAA,EACA9C,GAQAyP,EAAAjP,UAAAuU,OAAA,WAGA,GAAAzU,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8M,EAAAtT,KAAA,GAEA,IAAAN,EAAAqH,EAAAyN,MAAA7P,aAAA3E,KAAAuC,IAAAvC,KAAAwC,KAEA,OADAxC,KAAAwC,KAAA,EACA9C,GAOAyP,EAAAjP,UAAAmJ,MAAA,WACA,IAAAzN,EAAAoE,KAAAkU,SACAjX,EAAA+C,KAAAwC,IACAtF,EAAA8C,KAAAwC,IAAA5G,EAGA,GAAAsB,EAAA8C,KAAAwG,IACA,MAAA8M,EAAAtT,KAAApE,GAGA,OADAoE,KAAAwC,KAAA5G,EACAF,MAAAmW,QAAA7R,KAAAuC,KACAvC,KAAAuC,IAAA5E,MAAAV,EAAAC,GACAD,IAAAC,EACA,IAAA8C,KAAAuC,IAAAmI,YAAA,GACA1K,KAAAgU,EAAA1N,KAAAtG,KAAAuC,IAAAtF,EAAAC,IAOAiS,EAAAjP,UAAA5D,OAAA,WACA,IAAA+M,EAAArJ,KAAAqJ,QACA,OAAA9C,EAAAE,KAAA4C,EAAA,EAAAA,EAAAzN,SAQAuT,EAAAjP,UAAAwU,KAAA,SAAA9Y,GACA,GAAA,iBAAAA,EAAA,CAEA,GAAAoE,KAAAwC,IAAA5G,EAAAoE,KAAAwG,IACA,MAAA8M,EAAAtT,KAAApE,GACAoE,KAAAwC,KAAA5G,OAEA,GAEA,GAAAoE,KAAAwC,KAAAxC,KAAAwG,IACA,MAAA8M,EAAAtT,YACA,IAAAA,KAAAuC,IAAAvC,KAAAwC,QAEA,OAAAxC,MAQAmP,EAAAjP,UAAAyU,SAAA,SAAAzK,GACA,OAAAA,GACA,KAAA,EACAlK,KAAA0U,OACA,MACA,KAAA,EACA1U,KAAA0U,KAAA,GACA,MACA,KAAA,EACA1U,KAAA0U,KAAA1U,KAAAkU,UACA,MACA,KAAA,EACA,KAAA,IAAAhK,EAAA,EAAAlK,KAAAkU,WACAlU,KAAA2U,SAAAzK,GAEA,MACA,KAAA,EACAlK,KAAA0U,KAAA,GACA,MAGA,QACA,MAAAzW,MAAA,qBAAAiM,EAAA,cAAAlK,KAAAwC,KAEA,OAAAxC,MAGAmP,EAAAjB,EAAA,SAAA0G,GACAxF,EAAAwF,EAEA,IAAArZ,EAAAwL,EAAAwF,KAAA,SAAA,WACAxF,EAAA8N,MAAA1F,EAAAjP,UAAA,CAEA4U,MAAA,WACA,OAAApB,EAAApN,KAAAtG,MAAAzE,IAAA,IAGAwZ,OAAA,WACA,OAAArB,EAAApN,KAAAtG,MAAAzE,IAAA,IAGAyZ,OAAA,WACA,OAAAtB,EAAApN,KAAAtG,MAAAiV,WAAA1Z,IAAA,IAGA2Z,QAAA,WACA,OAAArB,EAAAvN,KAAAtG,MAAAzE,IAAA,IAGA4Z,SAAA,WACA,OAAAtB,EAAAvN,KAAAtG,MAAAzE,IAAA,mCC/YAF,EAAAC,QAAA8T,EAGA,IAAAD,EAAA/T,EAAA,KACAgU,EAAAlP,UAAAnB,OAAA0L,OAAA0E,EAAAjP,YAAAwK,YAAA0E,EAEA,IAAArI,EAAA3L,EAAA,IASA,SAAAgU,EAAApS,GACAmS,EAAA7I,KAAAtG,KAAAhD,GAUA+J,EAAA+M,SACA1E,EAAAlP,UAAA8T,EAAAjN,EAAA+M,OAAA5T,UAAAvC,OAKAyR,EAAAlP,UAAA5D,OAAA,WACA,IAAAkK,EAAAxG,KAAAkU,SACA,OAAAlU,KAAAuC,IAAA6S,UAAApV,KAAAwC,IAAAxC,KAAAwC,IAAA9F,KAAA2Y,IAAArV,KAAAwC,IAAAgE,EAAAxG,KAAAwG,yCClCAnL,EAAAC,QAAAiT,EAGA,IAAA3D,EAAAxP,EAAA,MACAmT,EAAArO,UAAAnB,OAAA0L,OAAAG,EAAA1K,YAAAwK,YAAA6D,GAAA5D,UAAA,OAEA,IAKAoB,EACAuJ,EACAC,EAPAzJ,EAAA1Q,EAAA,IACA0L,EAAA1L,EAAA,IACAwT,EAAAxT,EAAA,IACA2L,EAAA3L,EAAA,IAaA,SAAAmT,EAAAxN,GACA6J,EAAAtE,KAAAtG,KAAA,GAAAe,GAMAf,KAAAwV,SAAA,GAMAxV,KAAAyV,MAAA,GA6BA,SAAAC,KApBAnH,EAAAtD,SAAA,SAAAC,EAAAoD,GAKA,OAJAA,IACAA,EAAA,IAAAC,GACArD,EAAAnK,SACAuN,EAAAmD,WAAAvG,EAAAnK,SACAuN,EAAA2C,QAAA/F,EAAA2F,SAWAtC,EAAArO,UAAAyV,YAAA5O,EAAAxB,KAAAtJ,QAaAsS,EAAArO,UAAAmO,KAAA,SAAAA,EAAAvN,EAAAC,EAAAC,GACA,mBAAAD,IACAC,EAAAD,EACAA,EAAAjG,GAEA,IAAA8a,EAAA5V,KACA,IAAAgB,EACA,OAAA+F,EAAApG,UAAA0N,EAAAuH,EAAA9U,EAAAC,GAEA,IAAA8U,EAAA7U,IAAA0U,EAGA,SAAAI,EAAA3Z,EAAAmS,GAEA,GAAAtN,EAAA,CAEA,IAAA+U,EAAA/U,EAEA,GADAA,EAAA,KACA6U,EACA,MAAA1Z,EACA4Z,EAAA5Z,EAAAmS,IAIA,SAAA0H,EAAAlV,GACA,IAAAmV,EAAAnV,EAAAoV,YAAA,oBACA,IAAA,EAAAD,EAAA,CACA,IAAAE,EAAArV,EAAAsV,UAAAH,GACA,GAAAE,KAAAZ,EAAA,OAAAY,EAEA,OAAA,KAIA,SAAAE,EAAAvV,EAAArC,GACA,IAGA,GAFAsI,EAAAyE,SAAA/M,IAAA,MAAAA,EAAAhC,OAAA,KACAgC,EAAAmB,KAAA0V,MAAA7W,IACAsI,EAAAyE,SAAA/M,GAEA,CACA6W,EAAAxU,SAAAA,EACA,IACAkM,EADAsJ,EAAAhB,EAAA7W,EAAAmX,EAAA7U,GAEAjE,EAAA,EACA,GAAAwZ,EAAAC,QACA,KAAAzZ,EAAAwZ,EAAAC,QAAA3a,SAAAkB,GACAkQ,EAAAgJ,EAAAM,EAAAC,QAAAzZ,KAAA8Y,EAAAD,YAAA7U,EAAAwV,EAAAC,QAAAzZ,MACA4D,EAAAsM,GACA,GAAAsJ,EAAAE,YACA,IAAA1Z,EAAA,EAAAA,EAAAwZ,EAAAE,YAAA5a,SAAAkB,GACAkQ,EAAAgJ,EAAAM,EAAAE,YAAA1Z,KAAA8Y,EAAAD,YAAA7U,EAAAwV,EAAAE,YAAA1Z,MACA4D,EAAAsM,GAAA,QAbA4I,EAAAnE,WAAAhT,EAAAsC,SAAAkQ,QAAAxS,EAAAoS,QAeA,MAAA1U,GACA2Z,EAAA3Z,GAEA0Z,GAAAY,GACAX,EAAA,KAAAF,GAIA,SAAAlV,EAAAI,EAAA4V,GAGA,MAAA,EAAAd,EAAAH,MAAAhM,QAAA3I,IAKA,GAHA8U,EAAAH,MAAAjY,KAAAsD,GAGAA,KAAAyU,EACAM,EACAQ,EAAAvV,EAAAyU,EAAAzU,OAEA2V,EACAE,WAAA,aACAF,EACAJ,EAAAvV,EAAAyU,EAAAzU,YAOA,GAAA+U,EAAA,CACA,IAAApX,EACA,IACAA,EAAAsI,EAAAnG,GAAAgW,aAAA9V,GAAApC,SAAA,QACA,MAAAvC,GAGA,YAFAua,GACAZ,EAAA3Z,IAGAka,EAAAvV,EAAArC,SAEAgY,EACA1P,EAAArG,MAAAI,EAAA,SAAA3E,EAAAsC,KACAgY,EAEAzV,IAEA7E,EAEAua,EAEAD,GACAX,EAAA,KAAAF,GAFAE,EAAA3Z,GAKAka,EAAAvV,EAAArC,MAIA,IAAAgY,EAAA,EAIA1P,EAAAyE,SAAA1K,KACAA,EAAA,CAAAA,IACA,IAAA,IAAAkM,EAAAlQ,EAAA,EAAAA,EAAAgE,EAAAlF,SAAAkB,GACAkQ,EAAA4I,EAAAD,YAAA,GAAA7U,EAAAhE,MACA4D,EAAAsM,GAEA,OAAA6I,EACAD,GACAa,GACAX,EAAA,KAAAF,GACA9a,IAgCAyT,EAAArO,UAAAsO,SAAA,SAAA1N,EAAAC,GACA,IAAAgG,EAAA8P,OACA,MAAA5Y,MAAA,iBACA,OAAA+B,KAAAqO,KAAAvN,EAAAC,EAAA2U,IAMAnH,EAAArO,UAAA8R,WAAA,WACA,GAAAhS,KAAAwV,SAAA5Z,OACA,MAAAqC,MAAA,4BAAA+B,KAAAwV,SAAApN,IAAA,SAAAlB,GACA,MAAA,WAAAA,EAAAgF,OAAA,QAAAhF,EAAAgG,OAAAxF,WACA9J,KAAA,OACA,OAAAgN,EAAA1K,UAAA8R,WAAA1L,KAAAtG,OAIA,IAAA8W,EAAA,SAUA,SAAAC,EAAAzI,EAAApH,GACA,IAAA8P,EAAA9P,EAAAgG,OAAA+E,OAAA/K,EAAAgF,QACA,GAAA8K,EAAA,CACA,IAAAC,EAAA,IAAAnL,EAAA5E,EAAAQ,SAAAR,EAAAqB,GAAArB,EAAAU,KAAAV,EAAA+E,KAAAnR,EAAAoM,EAAAnG,SAIA,OAHAkW,EAAAxK,eAAAvF,GACAsF,eAAAyK,EACAD,EAAAzL,IAAA0L,IACA,EAEA,OAAA,EASA1I,EAAArO,UAAAyS,EAAA,SAAAxC,GACA,GAAAA,aAAArE,EAEAqE,EAAAjE,SAAApR,GAAAqV,EAAA3D,gBACAuK,EAAA/W,EAAAmQ,IACAnQ,KAAAwV,SAAAhY,KAAA2S,QAEA,GAAAA,aAAArJ,EAEAgQ,EAAA5Y,KAAAiS,EAAAjI,QACAiI,EAAAjD,OAAAiD,EAAAjI,MAAAiI,EAAA5I,aAEA,KAAA4I,aAAAvB,GAAA,CAEA,GAAAuB,aAAApE,EACA,IAAA,IAAAjP,EAAA,EAAAA,EAAAkD,KAAAwV,SAAA5Z,QACAmb,EAAA/W,EAAAA,KAAAwV,SAAA1Y,IACAkD,KAAAwV,SAAAjV,OAAAzD,EAAA,KAEAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAA6S,EAAAgB,YAAAvV,SAAA0B,EACA0C,KAAA2S,EAAAxC,EAAAW,EAAAxT,IACAwZ,EAAA5Y,KAAAiS,EAAAjI,QACAiI,EAAAjD,OAAAiD,EAAAjI,MAAAiI,KAcA5B,EAAArO,UAAA0S,EAAA,SAAAzC,GACA,GAAAA,aAAArE,GAEA,GAAAqE,EAAAjE,SAAApR,EACA,GAAAqV,EAAA3D,eACA2D,EAAA3D,eAAAU,OAAArB,OAAAsE,EAAA3D,gBACA2D,EAAA3D,eAAA,SACA,CACA,IAAA1Q,EAAAkE,KAAAwV,SAAA/L,QAAA0G,IAEA,EAAArU,GACAkE,KAAAwV,SAAAjV,OAAAzE,EAAA,SAIA,GAAAqU,aAAArJ,EAEAgQ,EAAA5Y,KAAAiS,EAAAjI,cACAiI,EAAAjD,OAAAiD,EAAAjI,WAEA,GAAAiI,aAAAvF,EAAA,CAEA,IAAA,IAAA9N,EAAA,EAAAA,EAAAqT,EAAAgB,YAAAvV,SAAAkB,EACAkD,KAAA4S,EAAAzC,EAAAW,EAAAhU,IAEAga,EAAA5Y,KAAAiS,EAAAjI,cACAiI,EAAAjD,OAAAiD,EAAAjI,QAMAqG,EAAAL,EAAA,SAAAC,EAAA+I,EAAAC,GACApL,EAAAoC,EACAmH,EAAA4B,EACA3B,EAAA4B,uDC9VA9b,EAAAC,QAAA,4BCKAA,EA6BAwT,QAAA1T,EAAA,gCClCAC,EAAAC,QAAAwT,EAEA,IAAA/H,EAAA3L,EAAA,IAsCA,SAAA0T,EAAAsI,EAAAC,EAAAC,GAEA,GAAA,mBAAAF,EACA,MAAArM,UAAA,8BAEAhE,EAAAhH,aAAAuG,KAAAtG,MAMAA,KAAAoX,QAAAA,EAMApX,KAAAqX,mBAAAA,EAMArX,KAAAsX,oBAAAA,IA1DAxI,EAAA5O,UAAAnB,OAAA0L,OAAA1D,EAAAhH,aAAAG,YAAAwK,YAAAoE,GAwEA5O,UAAAqX,QAAA,SAAAA,EAAAC,EAAAC,EAAAC,EAAAC,EAAA3W,GAEA,IAAA2W,EACA,MAAA5M,UAAA,6BAEA,IAAA6K,EAAA5V,KACA,IAAAgB,EACA,OAAA+F,EAAApG,UAAA4W,EAAA3B,EAAA4B,EAAAC,EAAAC,EAAAC,GAEA,IAAA/B,EAAAwB,QAEA,OADAT,WAAA,WAAA3V,EAAA/C,MAAA,mBAAA,GACAnD,EAGA,IACA,OAAA8a,EAAAwB,QACAI,EACAC,EAAA7B,EAAAyB,iBAAA,kBAAA,UAAAM,GAAA7B,SACA,SAAA3Z,EAAAsF,GAEA,GAAAtF,EAEA,OADAyZ,EAAApV,KAAA,QAAArE,EAAAqb,GACAxW,EAAA7E,GAGA,GAAA,OAAAsF,EAEA,OADAmU,EAAA1Y,KAAA,GACApC,EAGA,KAAA2G,aAAAiW,GACA,IACAjW,EAAAiW,EAAA9B,EAAA0B,kBAAA,kBAAA,UAAA7V,GACA,MAAAtF,GAEA,OADAyZ,EAAApV,KAAA,QAAArE,EAAAqb,GACAxW,EAAA7E,GAKA,OADAyZ,EAAApV,KAAA,OAAAiB,EAAA+V,GACAxW,EAAA,KAAAS,KAGA,MAAAtF,GAGA,OAFAyZ,EAAApV,KAAA,QAAArE,EAAAqb,GACAb,WAAA,WAAA3V,EAAA7E,IAAA,GACArB,IASAgU,EAAA5O,UAAAhD,IAAA,SAAA0a,GAOA,OANA5X,KAAAoX,UACAQ,GACA5X,KAAAoX,QAAA,KAAA,KAAA,MACApX,KAAAoX,QAAA,KACApX,KAAAQ,KAAA,OAAAH,OAEAL,kCC3IA3E,EAAAC,QAAAwT,EAGA,IAAAlE,EAAAxP,EAAA,MACA0T,EAAA5O,UAAAnB,OAAA0L,OAAAG,EAAA1K,YAAAwK,YAAAoE,GAAAnE,UAAA,UAEA,IAAAoE,EAAA3T,EAAA,IACA2L,EAAA3L,EAAA,IACAmU,EAAAnU,EAAA,IAWA,SAAA0T,EAAA5G,EAAAnH,GACA6J,EAAAtE,KAAAtG,KAAAkI,EAAAnH,GAMAf,KAAAsR,QAAA,GAOAtR,KAAA6X,EAAA,KAyDA,SAAA9G,EAAA+G,GAEA,OADAA,EAAAD,EAAA,KACAC,EA1CAhJ,EAAA7D,SAAA,SAAA/C,EAAAgD,GACA,IAAA4M,EAAA,IAAAhJ,EAAA5G,EAAAgD,EAAAnK,SAEA,GAAAmK,EAAAoG,QACA,IAAA,IAAAD,EAAAtS,OAAAC,KAAAkM,EAAAoG,SAAAxU,EAAA,EAAAA,EAAAuU,EAAAzV,SAAAkB,EACAgb,EAAAvM,IAAAwD,EAAA9D,SAAAoG,EAAAvU,GAAAoO,EAAAoG,QAAAD,EAAAvU,MAIA,OAHAoO,EAAA2F,QACAiH,EAAA7G,QAAA/F,EAAA2F,QACAiH,EAAAjN,QAAAK,EAAAL,QACAiN,GAQAhJ,EAAA5O,UAAAkL,OAAA,SAAAC,GACA,IAAA0M,EAAAnN,EAAA1K,UAAAkL,OAAA9E,KAAAtG,KAAAqL,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAvE,EAAAyB,SAAA,CACA,UAAAuP,GAAAA,EAAAhX,SAAAjG,EACA,UAAA8P,EAAA8F,YAAA1Q,KAAAgY,aAAA3M,IAAA,GACA,SAAA0M,GAAAA,EAAAlH,QAAA/V,EACA,UAAAwQ,EAAAtL,KAAA6K,QAAA/P,KAUAiE,OAAA4N,eAAAmC,EAAA5O,UAAA,eAAA,CACA0M,IAAA,WACA,OAAA5M,KAAA6X,IAAA7X,KAAA6X,EAAA9Q,EAAAmK,QAAAlR,KAAAsR,aAYAxC,EAAA5O,UAAA0M,IAAA,SAAA1E,GACA,OAAAlI,KAAAsR,QAAApJ,IACA0C,EAAA1K,UAAA0M,IAAAtG,KAAAtG,KAAAkI,IAMA4G,EAAA5O,UAAA8R,WAAA,WAEA,IADA,IAAAV,EAAAtR,KAAAgY,aACAlb,EAAA,EAAAA,EAAAwU,EAAA1V,SAAAkB,EACAwU,EAAAxU,GAAAb,UACA,OAAA2O,EAAA1K,UAAAjE,QAAAqK,KAAAtG,OAMA8O,EAAA5O,UAAAqL,IAAA,SAAA4E,GAGA,GAAAnQ,KAAA4M,IAAAuD,EAAAjI,MACA,MAAAjK,MAAA,mBAAAkS,EAAAjI,KAAA,QAAAlI,MAEA,OAAAmQ,aAAApB,EAGAgC,GAFA/Q,KAAAsR,QAAAnB,EAAAjI,MAAAiI,GACAjD,OAAAlN,MAGA4K,EAAA1K,UAAAqL,IAAAjF,KAAAtG,KAAAmQ,IAMArB,EAAA5O,UAAA2L,OAAA,SAAAsE,GACA,GAAAA,aAAApB,EAAA,CAGA,GAAA/O,KAAAsR,QAAAnB,EAAAjI,QAAAiI,EACA,MAAAlS,MAAAkS,EAAA,uBAAAnQ,MAIA,cAFAA,KAAAsR,QAAAnB,EAAAjI,MACAiI,EAAAjD,OAAA,KACA6D,EAAA/Q,MAEA,OAAA4K,EAAA1K,UAAA2L,OAAAvF,KAAAtG,KAAAmQ,IAUArB,EAAA5O,UAAAuK,OAAA,SAAA2M,EAAAC,EAAAC,GAEA,IADA,IACAE,EADAS,EAAA,IAAA1I,EAAAT,QAAAsI,EAAAC,EAAAC,GACAxa,EAAA,EAAAA,EAAAkD,KAAAgY,aAAApc,SAAAkB,EAAA,CACA,IAAAob,EAAAnR,EAAAoR,SAAAX,EAAAxX,KAAA6X,EAAA/a,IAAAb,UAAAiM,MAAA3I,QAAA,WAAA,IACA0Y,EAAAC,GAAAnR,EAAA5I,QAAA,CAAA,IAAA,KAAA4I,EAAAqR,WAAAF,GAAAA,EAAA,IAAAA,EAAAnR,CAAA,iCAAAA,CAAA,CACAsR,EAAAb,EACAc,EAAAd,EAAAjH,oBAAA9C,KACA8K,EAAAf,EAAAhH,qBAAA/C,OAGA,OAAAwK,iDCpKA5c,EAAAC,QAAAyQ,EAGA,IAAAnB,EAAAxP,EAAA,MACA2Q,EAAA7L,UAAAnB,OAAA0L,OAAAG,EAAA1K,YAAAwK,YAAAqB,GAAApB,UAAA,OAEA,IAAA7D,EAAA1L,EAAA,IACAwT,EAAAxT,EAAA,IACA0Q,EAAA1Q,EAAA,IACAyT,EAAAzT,EAAA,IACA0T,EAAA1T,EAAA,IACA4T,EAAA5T,EAAA,IACA+T,EAAA/T,EAAA,IACAiU,EAAAjU,EAAA,IACA2L,EAAA3L,EAAA,IACAqT,EAAArT,EAAA,IACAsT,EAAAtT,EAAA,IACAuT,EAAAvT,EAAA,IACAyL,EAAAzL,EAAA,IACA6T,EAAA7T,EAAA,IAUA,SAAA2Q,EAAA7D,EAAAnH,GACA6J,EAAAtE,KAAAtG,KAAAkI,EAAAnH,GAMAf,KAAAgI,OAAA,GAMAhI,KAAAwY,OAAA1d,EAMAkF,KAAAyY,WAAA3d,EAMAkF,KAAAgL,SAAAlQ,EAMAkF,KAAA2J,MAAA7O,EAOAkF,KAAA0Y,EAAA,KAOA1Y,KAAAwJ,EAAA,KAOAxJ,KAAA2Y,EAAA,KAOA3Y,KAAA4Y,EAAA,KA0HA,SAAA7H,EAAAnJ,GAKA,OAJAA,EAAA8Q,EAAA9Q,EAAA4B,EAAA5B,EAAA+Q,EAAA,YACA/Q,EAAA7K,cACA6K,EAAA9J,cACA8J,EAAAsI,OACAtI,EA5HA7I,OAAA0T,iBAAA1G,EAAA7L,UAAA,CAQA2Y,WAAA,CACAjM,IAAA,WAGA,GAAA5M,KAAA0Y,EACA,OAAA1Y,KAAA0Y,EAEA1Y,KAAA0Y,EAAA,GACA,IAAA,IAAArH,EAAAtS,OAAAC,KAAAgB,KAAAgI,QAAAlL,EAAA,EAAAA,EAAAuU,EAAAzV,SAAAkB,EAAA,CACA,IAAAoK,EAAAlH,KAAAgI,OAAAqJ,EAAAvU,IACAyL,EAAArB,EAAAqB,GAGA,GAAAvI,KAAA0Y,EAAAnQ,GACA,MAAAtK,MAAA,gBAAAsK,EAAA,OAAAvI,MAEAA,KAAA0Y,EAAAnQ,GAAArB,EAEA,OAAAlH,KAAA0Y,IAUAzQ,YAAA,CACA2E,IAAA,WACA,OAAA5M,KAAAwJ,IAAAxJ,KAAAwJ,EAAAzC,EAAAmK,QAAAlR,KAAAgI,WAUA8Q,YAAA,CACAlM,IAAA,WACA,OAAA5M,KAAA2Y,IAAA3Y,KAAA2Y,EAAA5R,EAAAmK,QAAAlR,KAAAwY,WAUA/K,KAAA,CACAb,IAAA,WACA,OAAA5M,KAAA4Y,IAAA5Y,KAAAyN,KAAA1B,EAAAgN,oBAAA/Y,KAAA+L,KAEAoH,IAAA,SAAA1F,GAGA,IAAAvN,EAAAuN,EAAAvN,UACAA,aAAA8O,KACAvB,EAAAvN,UAAA,IAAA8O,GAAAtE,YAAA+C,EACA1G,EAAA8N,MAAApH,EAAAvN,UAAAA,IAIAuN,EAAAoC,MAAApC,EAAAvN,UAAA2P,MAAA7P,KAGA+G,EAAA8N,MAAApH,EAAAuB,GAAA,GAEAhP,KAAA4Y,EAAAnL,EAIA,IADA,IAAA3Q,EAAA,EACAA,EAAAkD,KAAAiI,YAAArM,SAAAkB,EACAkD,KAAAwJ,EAAA1M,GAAAb,UAGA,IAAA+c,EAAA,GACA,IAAAlc,EAAA,EAAAA,EAAAkD,KAAA8Y,YAAAld,SAAAkB,EACAkc,EAAAhZ,KAAA2Y,EAAA7b,GAAAb,UAAAiM,MAAA,CACA0E,IAAA7F,EAAAmM,YAAAlT,KAAA2Y,EAAA7b,GAAAiW,OACAI,IAAApM,EAAAqM,YAAApT,KAAA2Y,EAAA7b,GAAAiW,QAEAjW,GACAiC,OAAA0T,iBAAAhF,EAAAvN,UAAA8Y,OAUAjN,EAAAgN,oBAAA,SAAAhR,GAIA,IAFA,IAEAb,EAFAD,EAAAF,EAAA5I,QAAA,CAAA,KAAA4J,EAAAG,MAEApL,EAAA,EAAAA,EAAAiL,EAAAE,YAAArM,SAAAkB,GACAoK,EAAAa,EAAAyB,EAAA1M,IAAAsL,IAAAnB,EACA,YAAAF,EAAAoB,SAAAjB,EAAAgB,OACAhB,EAAAM,UAAAP,EACA,YAAAF,EAAAoB,SAAAjB,EAAAgB,OACA,OAAAjB,EACA,wEADAA,CAEA,yBA6BA8E,EAAAd,SAAA,SAAA/C,EAAAgD,GACA,IAAAtD,EAAA,IAAAmE,EAAA7D,EAAAgD,EAAAnK,SACA6G,EAAA6Q,WAAAvN,EAAAuN,WACA7Q,EAAAoD,SAAAE,EAAAF,SAGA,IAFA,IAAAqG,EAAAtS,OAAAC,KAAAkM,EAAAlD,QACAlL,EAAA,EACAA,EAAAuU,EAAAzV,SAAAkB,EACA8K,EAAA2D,UACA,IAAAL,EAAAlD,OAAAqJ,EAAAvU,IAAA8M,QACAiF,EAAA5D,SACAa,EAAAb,UAAAoG,EAAAvU,GAAAoO,EAAAlD,OAAAqJ,EAAAvU,MAEA,GAAAoO,EAAAsN,OACA,IAAAnH,EAAAtS,OAAAC,KAAAkM,EAAAsN,QAAA1b,EAAA,EAAAA,EAAAuU,EAAAzV,SAAAkB,EACA8K,EAAA2D,IAAAqD,EAAA3D,SAAAoG,EAAAvU,GAAAoO,EAAAsN,OAAAnH,EAAAvU,MACA,GAAAoO,EAAA2F,OACA,IAAAQ,EAAAtS,OAAAC,KAAAkM,EAAA2F,QAAA/T,EAAA,EAAAA,EAAAuU,EAAAzV,SAAAkB,EAAA,CACA,IAAA+T,EAAA3F,EAAA2F,OAAAQ,EAAAvU,IACA8K,EAAA2D,KACAsF,EAAAtI,KAAAzN,EACAgR,EAAAb,SACA4F,EAAA7I,SAAAlN,EACAiR,EAAAd,SACA4F,EAAAtJ,SAAAzM,EACAgM,EAAAmE,SACA4F,EAAAS,UAAAxW,EACAgU,EAAA7D,SACAL,EAAAK,UAAAoG,EAAAvU,GAAA+T,IAWA,OARA3F,EAAAuN,YAAAvN,EAAAuN,WAAA7c,SACAgM,EAAA6Q,WAAAvN,EAAAuN,YACAvN,EAAAF,UAAAE,EAAAF,SAAApP,SACAgM,EAAAoD,SAAAE,EAAAF,UACAE,EAAAvB,QACA/B,EAAA+B,OAAA,GACAuB,EAAAL,UACAjD,EAAAiD,QAAAK,EAAAL,SACAjD,GAQAmE,EAAA7L,UAAAkL,OAAA,SAAAC,GACA,IAAA0M,EAAAnN,EAAA1K,UAAAkL,OAAA9E,KAAAtG,KAAAqL,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAvE,EAAAyB,SAAA,CACA,UAAAuP,GAAAA,EAAAhX,SAAAjG,EACA,SAAA8P,EAAA8F,YAAA1Q,KAAA8Y,YAAAzN,GACA,SAAAT,EAAA8F,YAAA1Q,KAAAiI,YAAAyB,OAAA,SAAAkH,GAAA,OAAAA,EAAAnE,iBAAApB,IAAA,GACA,aAAArL,KAAAyY,YAAAzY,KAAAyY,WAAA7c,OAAAoE,KAAAyY,WAAA3d,EACA,WAAAkF,KAAAgL,UAAAhL,KAAAgL,SAAApP,OAAAoE,KAAAgL,SAAAlQ,EACA,QAAAkF,KAAA2J,OAAA7O,EACA,SAAAid,GAAAA,EAAAlH,QAAA/V,EACA,UAAAwQ,EAAAtL,KAAA6K,QAAA/P,KAOAiR,EAAA7L,UAAA8R,WAAA,WAEA,IADA,IAAAhK,EAAAhI,KAAAiI,YAAAnL,EAAA,EACAA,EAAAkL,EAAApM,QACAoM,EAAAlL,KAAAb,UACA,IAAAuc,EAAAxY,KAAA8Y,YACA,IADAhc,EAAA,EACAA,EAAA0b,EAAA5c,QACA4c,EAAA1b,KAAAb,UACA,OAAA2O,EAAA1K,UAAA8R,WAAA1L,KAAAtG,OAMA+L,EAAA7L,UAAA0M,IAAA,SAAA1E,GACA,OAAAlI,KAAAgI,OAAAE,IACAlI,KAAAwY,QAAAxY,KAAAwY,OAAAtQ,IACAlI,KAAA6Q,QAAA7Q,KAAA6Q,OAAA3I,IACA,MAUA6D,EAAA7L,UAAAqL,IAAA,SAAA4E,GAEA,GAAAnQ,KAAA4M,IAAAuD,EAAAjI,MACA,MAAAjK,MAAA,mBAAAkS,EAAAjI,KAAA,QAAAlI,MAEA,GAAAmQ,aAAArE,GAAAqE,EAAAjE,SAAApR,EAAA,CAMA,GAAAkF,KAAA0Y,EAAA1Y,KAAA0Y,EAAAvI,EAAA5H,IAAAvI,KAAA6Y,WAAA1I,EAAA5H,IACA,MAAAtK,MAAA,gBAAAkS,EAAA5H,GAAA,OAAAvI,MACA,GAAAA,KAAA0L,aAAAyE,EAAA5H,IACA,MAAAtK,MAAA,MAAAkS,EAAA5H,GAAA,mBAAAvI,MACA,GAAAA,KAAA2L,eAAAwE,EAAAjI,MACA,MAAAjK,MAAA,SAAAkS,EAAAjI,KAAA,oBAAAlI,MAOA,OALAmQ,EAAAjD,QACAiD,EAAAjD,OAAArB,OAAAsE,IACAnQ,KAAAgI,OAAAmI,EAAAjI,MAAAiI,GACA9D,QAAArM,KACAmQ,EAAAuB,MAAA1R,MACA+Q,EAAA/Q,MAEA,OAAAmQ,aAAAvB,GACA5O,KAAAwY,SACAxY,KAAAwY,OAAA,KACAxY,KAAAwY,OAAArI,EAAAjI,MAAAiI,GACAuB,MAAA1R,MACA+Q,EAAA/Q,OAEA4K,EAAA1K,UAAAqL,IAAAjF,KAAAtG,KAAAmQ,IAUApE,EAAA7L,UAAA2L,OAAA,SAAAsE,GACA,GAAAA,aAAArE,GAAAqE,EAAAjE,SAAApR,EAAA,CAIA,IAAAkF,KAAAgI,QAAAhI,KAAAgI,OAAAmI,EAAAjI,QAAAiI,EACA,MAAAlS,MAAAkS,EAAA,uBAAAnQ,MAKA,cAHAA,KAAAgI,OAAAmI,EAAAjI,MACAiI,EAAAjD,OAAA,KACAiD,EAAAwB,SAAA3R,MACA+Q,EAAA/Q,MAEA,GAAAmQ,aAAAvB,EAAA,CAGA,IAAA5O,KAAAwY,QAAAxY,KAAAwY,OAAArI,EAAAjI,QAAAiI,EACA,MAAAlS,MAAAkS,EAAA,uBAAAnQ,MAKA,cAHAA,KAAAwY,OAAArI,EAAAjI,MACAiI,EAAAjD,OAAA,KACAiD,EAAAwB,SAAA3R,MACA+Q,EAAA/Q,MAEA,OAAA4K,EAAA1K,UAAA2L,OAAAvF,KAAAtG,KAAAmQ,IAQApE,EAAA7L,UAAAwL,aAAA,SAAAnD,GACA,OAAAqC,EAAAc,aAAA1L,KAAAgL,SAAAzC,IAQAwD,EAAA7L,UAAAyL,eAAA,SAAAzD,GACA,OAAA0C,EAAAe,eAAA3L,KAAAgL,SAAA9C,IAQA6D,EAAA7L,UAAAuK,OAAA,SAAAmF,GACA,OAAA,IAAA5P,KAAAyN,KAAAmC,IAOA7D,EAAA7L,UAAA+Y,MAAA,WAMA,IAFA,IAAAvR,EAAA1H,KAAA0H,SACAmC,EAAA,GACA/M,EAAA,EAAAA,EAAAkD,KAAAiI,YAAArM,SAAAkB,EACA+M,EAAArM,KAAAwC,KAAAwJ,EAAA1M,GAAAb,UAAAqL,cAGAtH,KAAAjD,OAAA0R,EAAAzO,KAAAyO,CAAA,CACAY,OAAAA,EACAxF,MAAAA,EACA9C,KAAAA,IAEA/G,KAAAlC,OAAA4Q,EAAA1O,KAAA0O,CAAA,CACAS,OAAAA,EACAtF,MAAAA,EACA9C,KAAAA,IAEA/G,KAAAkQ,OAAAvB,EAAA3O,KAAA2O,CAAA,CACA9E,MAAAA,EACA9C,KAAAA,IAEA/G,KAAA8H,WAAAjB,EAAAiB,WAAA9H,KAAA6G,CAAA,CACAgD,MAAAA,EACA9C,KAAAA,IAEA/G,KAAAwI,SAAA3B,EAAA2B,SAAAxI,KAAA6G,CAAA,CACAgD,MAAAA,EACA9C,KAAAA,IAIA,IAAAmS,EAAAjK,EAAAvH,GACA,GAAAwR,EAAA,CACA,IAAAC,EAAApa,OAAA0L,OAAAzK,MAEAmZ,EAAArR,WAAA9H,KAAA8H,WACA9H,KAAA8H,WAAAoR,EAAApR,WAAAhE,KAAAqV,GAGAA,EAAA3Q,SAAAxI,KAAAwI,SACAxI,KAAAwI,SAAA0Q,EAAA1Q,SAAA1E,KAAAqV,GAIA,OAAAnZ,MASA+L,EAAA7L,UAAAnD,OAAA,SAAAsP,EAAAyD,GACA,OAAA9P,KAAAiZ,QAAAlc,OAAAsP,EAAAyD,IASA/D,EAAA7L,UAAA6P,gBAAA,SAAA1D,EAAAyD,GACA,OAAA9P,KAAAjD,OAAAsP,EAAAyD,GAAAA,EAAAtJ,IAAAsJ,EAAAsJ,OAAAtJ,GAAAuJ,UAWAtN,EAAA7L,UAAApC,OAAA,SAAAkS,EAAApU,GACA,OAAAoE,KAAAiZ,QAAAnb,OAAAkS,EAAApU,IAUAmQ,EAAA7L,UAAA+P,gBAAA,SAAAD,GAGA,OAFAA,aAAAb,IACAa,EAAAb,EAAA1E,OAAAuF,IACAhQ,KAAAlC,OAAAkS,EAAAA,EAAAkE,WAQAnI,EAAA7L,UAAAgQ,OAAA,SAAA7D,GACA,OAAArM,KAAAiZ,QAAA/I,OAAA7D,IAQAN,EAAA7L,UAAA4H,WAAA,SAAAqI,GACA,OAAAnQ,KAAAiZ,QAAAnR,WAAAqI,IA4BApE,EAAA7L,UAAAsI,SAAA,SAAA6D,EAAAtL,GACA,OAAAf,KAAAiZ,QAAAzQ,SAAA6D,EAAAtL,IAkBAgL,EAAA2B,EAAA,SAAA4L,GACA,OAAA,SAAAC,GACAxS,EAAA+G,aAAAyL,EAAAD,uHCpkBA,IAAAzP,EAAAvO,EAEAyL,EAAA3L,EAAA,IAEAmd,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAAiB,EAAAjS,EAAA1L,GACA,IAAAiB,EAAA,EAAA2c,EAAA,GAEA,IADA5d,GAAA,EACAiB,EAAAyK,EAAA3L,QAAA6d,EAAAlB,EAAAzb,EAAAjB,IAAA0L,EAAAzK,KACA,OAAA2c,EAuBA5P,EAAAC,MAAA0P,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAwBA3P,EAAAoD,SAAAuM,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,EACA,GACAzS,EAAAyG,WACA,OAaA3D,EAAAb,KAAAwQ,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,GAmBA3P,EAAAM,OAAAqP,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,GAoBA3P,EAAAE,OAAAyP,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,gCC5LA,IAIAzN,EACAjF,EALAC,EAAA1L,EAAAC,QAAAF,EAAA,IAEAoU,EAAApU,EAAA,IAKA2L,EAAA5I,QAAA/C,EAAA,GACA2L,EAAArG,MAAAtF,EAAA,GACA2L,EAAAxB,KAAAnK,EAAA,GAMA2L,EAAAnG,GAAAmG,EAAAlG,QAAA,MAOAkG,EAAAmK,QAAA,SAAAf,GACA,GAAAA,EAAA,CAIA,IAHA,IAAAnR,EAAAD,OAAAC,KAAAmR,GACAQ,EAAAjV,MAAAsD,EAAApD,QACAE,EAAA,EACAA,EAAAkD,EAAApD,QACA+U,EAAA7U,GAAAqU,EAAAnR,EAAAlD,MACA,OAAA6U,EAEA,MAAA,IAQA5J,EAAAyB,SAAA,SAAAmI,GAGA,IAFA,IAAAR,EAAA,GACArU,EAAA,EACAA,EAAA6U,EAAA/U,QAAA,CACA,IAAA0O,EAAAqG,EAAA7U,KACAwG,EAAAqO,EAAA7U,KACAwG,IAAAxH,IACAqV,EAAA7F,GAAAhI,GAEA,OAAA6N,GAGA,IAAAuJ,EAAA,MACAC,EAAA,KAOA5S,EAAAqR,WAAA,SAAAlQ,GACA,MAAA,uTAAAhK,KAAAgK,IAQAnB,EAAAoB,SAAA,SAAAf,GACA,OAAA,YAAAlJ,KAAAkJ,IAAAL,EAAAqR,WAAAhR,GACA,KAAAA,EAAA7H,QAAAma,EAAA,QAAAna,QAAAoa,EAAA,OAAA,KACA,IAAAvS,GAQAL,EAAA6S,QAAA,SAAAC,GACA,OAAAA,EAAApd,OAAA,GAAAqd,cAAAD,EAAAzD,UAAA,IAGA,IAAA2D,EAAA,YAOAhT,EAAAiT,UAAA,SAAAH,GACA,OAAAA,EAAAzD,UAAA,EAAA,GACAyD,EAAAzD,UAAA,GACA7W,QAAAwa,EAAA,SAAAva,EAAAC,GAAA,OAAAA,EAAAqa,iBASA/S,EAAA2B,kBAAA,SAAAuR,EAAA1c,GACA,OAAA0c,EAAA1R,GAAAhL,EAAAgL,IAWAxB,EAAA+G,aAAA,SAAAL,EAAA6L,GAGA,GAAA7L,EAAAoC,MAMA,OALAyJ,GAAA7L,EAAAoC,MAAA3H,OAAAoR,IACAvS,EAAAmT,aAAArO,OAAA4B,EAAAoC,OACApC,EAAAoC,MAAA3H,KAAAoR,EACAvS,EAAAmT,aAAA3O,IAAAkC,EAAAoC,QAEApC,EAAAoC,MAIA9D,IACAA,EAAA3Q,EAAA,KAEA,IAAAwM,EAAA,IAAAmE,EAAAuN,GAAA7L,EAAAvF,MAKA,OAJAnB,EAAAmT,aAAA3O,IAAA3D,GACAA,EAAA6F,KAAAA,EACA1O,OAAA4N,eAAAc,EAAA,QAAA,CAAA/N,MAAAkI,EAAAuS,YAAA,IACApb,OAAA4N,eAAAc,EAAAvN,UAAA,QAAA,CAAAR,MAAAkI,EAAAuS,YAAA,IACAvS,GAGA,IAAAwS,EAAA,EAOArT,EAAAgH,aAAA,SAAAoC,GAGA,GAAAA,EAAAN,MACA,OAAAM,EAAAN,MAGA/I,IACAA,EAAA1L,EAAA,KAEA,IAAA+P,EAAA,IAAArE,EAAA,OAAAsT,IAAAjK,GAGA,OAFApJ,EAAAmT,aAAA3O,IAAAJ,GACApM,OAAA4N,eAAAwD,EAAA,QAAA,CAAAzQ,MAAAyL,EAAAgP,YAAA,IACAhP,GASApM,OAAA4N,eAAA5F,EAAA,eAAA,CACA6F,IAAA,WACA,OAAA4C,EAAA,YAAAA,EAAA,UAAA,IAAApU,EAAA,yEC9KAC,EAAAC,QAAA+X,EAEA,IAAAtM,EAAA3L,EAAA,IAUA,SAAAiY,EAAApO,EAAAC,GASAlF,KAAAiF,GAAAA,IAAA,EAMAjF,KAAAkF,GAAAA,IAAA,EAQA,IAAAmV,EAAAhH,EAAAgH,KAAA,IAAAhH,EAAA,EAAA,GAEAgH,EAAAjR,SAAA,WAAA,OAAA,GACAiR,EAAAC,SAAAD,EAAApF,SAAA,WAAA,OAAAjV,MACAqa,EAAAze,OAAA,WAAA,OAAA,GAOA,IAAA2e,EAAAlH,EAAAkH,SAAA,mBAOAlH,EAAAjG,WAAA,SAAA1N,GACA,GAAA,IAAAA,EACA,OAAA2a,EACA,IAAAnX,EAAAxD,EAAA,EACAwD,IACAxD,GAAAA,GACA,IAAAuF,EAAAvF,IAAA,EACAwF,GAAAxF,EAAAuF,GAAA,aAAA,EAUA,OATA/B,IACAgC,GAAAA,IAAA,EACAD,GAAAA,IAAA,EACA,aAAAA,IACAA,EAAA,EACA,aAAAC,IACAA,EAAA,KAGA,IAAAmO,EAAApO,EAAAC,IAQAmO,EAAAmH,KAAA,SAAA9a,GACA,GAAA,iBAAAA,EACA,OAAA2T,EAAAjG,WAAA1N,GACA,GAAAqH,EAAAyE,SAAA9L,GAAA,CAEA,IAAAqH,EAAAwF,KAGA,OAAA8G,EAAAjG,WAAAqN,SAAA/a,EAAA,KAFAA,EAAAqH,EAAAwF,KAAAmO,WAAAhb,GAIA,OAAAA,EAAAuJ,KAAAvJ,EAAAwJ,KAAA,IAAAmK,EAAA3T,EAAAuJ,MAAA,EAAAvJ,EAAAwJ,OAAA,GAAAmR,GAQAhH,EAAAnT,UAAAkJ,SAAA,SAAAD,GACA,IAAAA,GAAAnJ,KAAAkF,KAAA,GAAA,CACA,IAAAD,EAAA,GAAAjF,KAAAiF,KAAA,EACAC,GAAAlF,KAAAkF,KAAA,EAGA,OAFAD,IACAC,EAAAA,EAAA,IAAA,KACAD,EAAA,WAAAC,GAEA,OAAAlF,KAAAiF,GAAA,WAAAjF,KAAAkF,IAQAmO,EAAAnT,UAAAya,OAAA,SAAAxR,GACA,OAAApC,EAAAwF,KACA,IAAAxF,EAAAwF,KAAA,EAAAvM,KAAAiF,GAAA,EAAAjF,KAAAkF,KAAAiE,GAEA,CAAAF,IAAA,EAAAjJ,KAAAiF,GAAAiE,KAAA,EAAAlJ,KAAAkF,GAAAiE,WAAAA,IAGA,IAAAnL,EAAAP,OAAAyC,UAAAlC,WAOAqV,EAAAuH,SAAA,SAAAC,GACA,OAAAA,IAAAN,EACAF,EACA,IAAAhH,GACArV,EAAAsI,KAAAuU,EAAA,GACA7c,EAAAsI,KAAAuU,EAAA,IAAA,EACA7c,EAAAsI,KAAAuU,EAAA,IAAA,GACA7c,EAAAsI,KAAAuU,EAAA,IAAA,MAAA,GAEA7c,EAAAsI,KAAAuU,EAAA,GACA7c,EAAAsI,KAAAuU,EAAA,IAAA,EACA7c,EAAAsI,KAAAuU,EAAA,IAAA,GACA7c,EAAAsI,KAAAuU,EAAA,IAAA,MAAA,IAQAxH,EAAAnT,UAAA4a,OAAA,WACA,OAAArd,OAAAC,aACA,IAAAsC,KAAAiF,GACAjF,KAAAiF,KAAA,EAAA,IACAjF,KAAAiF,KAAA,GAAA,IACAjF,KAAAiF,KAAA,GACA,IAAAjF,KAAAkF,GACAlF,KAAAkF,KAAA,EAAA,IACAlF,KAAAkF,KAAA,GAAA,IACAlF,KAAAkF,KAAA,KAQAmO,EAAAnT,UAAAoa,SAAA,WACA,IAAAS,EAAA/a,KAAAkF,IAAA,GAGA,OAFAlF,KAAAkF,KAAAlF,KAAAkF,IAAA,EAAAlF,KAAAiF,KAAA,IAAA8V,KAAA,EACA/a,KAAAiF,IAAAjF,KAAAiF,IAAA,EAAA8V,KAAA,EACA/a,MAOAqT,EAAAnT,UAAA+U,SAAA,WACA,IAAA8F,IAAA,EAAA/a,KAAAiF,IAGA,OAFAjF,KAAAiF,KAAAjF,KAAAiF,KAAA,EAAAjF,KAAAkF,IAAA,IAAA6V,KAAA,EACA/a,KAAAkF,IAAAlF,KAAAkF,KAAA,EAAA6V,KAAA,EACA/a,MAOAqT,EAAAnT,UAAAtE,OAAA,WACA,IAAAof,EAAAhb,KAAAiF,GACAgW,GAAAjb,KAAAiF,KAAA,GAAAjF,KAAAkF,IAAA,KAAA,EACAgW,EAAAlb,KAAAkF,KAAA,GACA,OAAA,IAAAgW,EACA,IAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,kCCrMA,IAAAnU,EAAAzL,EA4NA,SAAAuZ,EAAAsG,EAAAC,EAAArO,GACA,IAAA,IAAA/N,EAAAD,OAAAC,KAAAoc,GAAAte,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAqe,EAAAnc,EAAAlC,MAAAhC,GAAAiS,IACAoO,EAAAnc,EAAAlC,IAAAse,EAAApc,EAAAlC,KACA,OAAAqe,EAoBA,SAAAE,EAAAnT,GAEA,SAAAoT,EAAAjP,EAAAuD,GAEA,KAAA5P,gBAAAsb,GACA,OAAA,IAAAA,EAAAjP,EAAAuD,GAKA7Q,OAAA4N,eAAA3M,KAAA,UAAA,CAAA4M,IAAA,WAAA,OAAAP,KAGApO,MAAAsd,kBACAtd,MAAAsd,kBAAAvb,KAAAsb,GAEAvc,OAAA4N,eAAA3M,KAAA,QAAA,CAAAN,MAAAzB,QAAAud,OAAA,KAEA5L,GACAiF,EAAA7U,KAAA4P,GAWA,OARA0L,EAAApb,UAAAnB,OAAA0L,OAAAxM,MAAAiC,YAAAwK,YAAA4Q,EAEAvc,OAAA4N,eAAA2O,EAAApb,UAAA,OAAA,CAAA0M,IAAA,WAAA,OAAA1E,KAEAoT,EAAApb,UAAAxB,SAAA,WACA,OAAAsB,KAAAkI,KAAA,KAAAlI,KAAAqM,SAGAiP,EA/QAvU,EAAApG,UAAAvF,EAAA,GAGA2L,EAAA1K,OAAAjB,EAAA,GAGA2L,EAAAhH,aAAA3E,EAAA,GAGA2L,EAAAyN,MAAApZ,EAAA,GAGA2L,EAAAlG,QAAAzF,EAAA,GAGA2L,EAAAR,KAAAnL,EAAA,IAGA2L,EAAA0U,KAAArgB,EAAA,GAGA2L,EAAAsM,SAAAjY,EAAA,IAGA2L,EAAA2U,OAAA,oBAAAC,QAAAA,QACA,oBAAAD,QAAAA,QACA,oBAAA9F,MAAAA,MACA5V,KAQA+G,EAAAyG,WAAAzO,OAAAsO,OAAAtO,OAAAsO,OAAA,IAAA,GAOAtG,EAAAwG,YAAAxO,OAAAsO,OAAAtO,OAAAsO,OAAA,IAAA,GAQAtG,EAAA8P,UAAA9P,EAAA2U,OAAArF,SAAAtP,EAAA2U,OAAArF,QAAAuF,UAAA7U,EAAA2U,OAAArF,QAAAuF,SAAAC,MAQA9U,EAAA0E,UAAAqQ,OAAArQ,WAAA,SAAA/L,GACA,MAAA,iBAAAA,GAAAqc,SAAArc,IAAAhD,KAAAiD,MAAAD,KAAAA,GAQAqH,EAAAyE,SAAA,SAAA9L,GACA,MAAA,iBAAAA,GAAAA,aAAAjC,QAQAsJ,EAAAoF,SAAA,SAAAzM,GACA,OAAAA,GAAA,iBAAAA,GAWAqH,EAAAiV,MAQAjV,EAAAkV,MAAA,SAAArL,EAAAxJ,GACA,IAAA1H,EAAAkR,EAAAxJ,GACA,QAAA,MAAA1H,IAAAkR,EAAAsL,eAAA9U,MACA,iBAAA1H,GAAA,GAAAhE,MAAAmW,QAAAnS,GAAAA,EAAA9D,OAAAmD,OAAAC,KAAAU,GAAA9D,UAeAmL,EAAA+M,OAAA,WACA,IACA,IAAAA,EAAA/M,EAAAlG,QAAA,UAAAiT,OAEA,OAAAA,EAAA5T,UAAAic,UAAArI,EAAA,KACA,MAAAxO,GAEA,OAAA,MAPA,GAYAyB,EAAAqV,EAAA,KAGArV,EAAAsV,EAAA,KAOAtV,EAAAuG,UAAA,SAAAgP,GAEA,MAAA,iBAAAA,EACAvV,EAAA+M,OACA/M,EAAAsV,EAAAC,GACA,IAAAvV,EAAArL,MAAA4gB,GACAvV,EAAA+M,OACA/M,EAAAqV,EAAAE,GACA,oBAAA3a,WACA2a,EACA,IAAA3a,WAAA2a,IAOAvV,EAAArL,MAAA,oBAAAiG,WAAAA,WAAAjG,MAOAqL,EAAAwF,KAAA,oBAAA8J,SAAAA,QAAAkG,IAAAC,YAAAzV,EAAA2U,OAAAe,SAAA1V,EAAA2U,OAAAe,QAAAlQ,MACAxF,EAAA2U,OAAAnP,MACAxF,EAAAlG,QAAA,QAAA/F,EAOAiM,EAAA2V,OAAA,mBAOA3V,EAAA4V,QAAA,wBAOA5V,EAAA6V,QAAA,6CAOA7V,EAAA8V,WAAA,SAAAnd,GACA,OAAAA,EACAqH,EAAAsM,SAAAmH,KAAA9a,GAAAob,SACA/T,EAAAsM,SAAAkH,UASAxT,EAAA+V,aAAA,SAAAjC,EAAA1R,GACA,IAAAwK,EAAA5M,EAAAsM,SAAAuH,SAAAC,GACA,OAAA9T,EAAAwF,KACAxF,EAAAwF,KAAAwQ,SAAApJ,EAAA1O,GAAA0O,EAAAzO,GAAAiE,GACAwK,EAAAvK,WAAAD,IAkBApC,EAAA8N,MAAAA,EAOA9N,EAAAoR,QAAA,SAAA0B,GACA,OAAAA,EAAApd,OAAA,GAAA2P,cAAAyN,EAAAzD,UAAA,IA0CArP,EAAAsU,SAAAA,EAmBAtU,EAAAiW,cAAA3B,EAAA,iBAoBAtU,EAAAmM,YAAA,SAAAJ,GAEA,IADA,IAAAmK,EAAA,GACAngB,EAAA,EAAAA,EAAAgW,EAAAlX,SAAAkB,EACAmgB,EAAAnK,EAAAhW,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAgB,MAAAlD,EAAAkC,EAAApD,OAAA,GAAA,EAAAkB,IAAAA,EACA,GAAA,IAAAmgB,EAAAje,EAAAlC,KAAAkD,KAAAhB,EAAAlC,MAAAhC,GAAA,OAAAkF,KAAAhB,EAAAlC,IACA,OAAAkC,EAAAlC,KAiBAiK,EAAAqM,YAAA,SAAAN,GAQA,OAAA,SAAA5K,GACA,IAAA,IAAApL,EAAA,EAAAA,EAAAgW,EAAAlX,SAAAkB,EACAgW,EAAAhW,KAAAoL,UACAlI,KAAA8S,EAAAhW,MAoBAiK,EAAAsE,cAAA,CACA6R,MAAAzf,OACA0f,MAAA1f,OACA4L,MAAA5L,OACAyN,MAAA,GAIAnE,EAAAmH,EAAA,WACA,IAAA4F,EAAA/M,EAAA+M,OAEAA,GAMA/M,EAAAqV,EAAAtI,EAAA0G,OAAA7Y,WAAA6Y,MAAA1G,EAAA0G,MAEA,SAAA9a,EAAA0d,GACA,OAAA,IAAAtJ,EAAApU,EAAA0d,IAEArW,EAAAsV,EAAAvI,EAAAuJ,aAEA,SAAAnX,GACA,OAAA,IAAA4N,EAAA5N,KAbAa,EAAAqV,EAAArV,EAAAsV,EAAA,gECrYAhhB,EAAAC,QAwHA,SAAAyM,GAGA,IAAAd,EAAAF,EAAA5I,QAAA,CAAA,KAAA4J,EAAAG,KAAA,UAAAnB,CACA,oCADAA,CAEA,WAAA,mBACAyR,EAAAzQ,EAAA+Q,YACAwE,EAAA,GACA9E,EAAA5c,QAAAqL,EACA,YAEA,IAAA,IAAAnK,EAAA,EAAAA,EAAAiL,EAAAE,YAAArM,SAAAkB,EAAA,CACA,IAAAoK,EAAAa,EAAAyB,EAAA1M,GAAAb,UACAoL,EAAA,IAAAN,EAAAoB,SAAAjB,EAAAgB,MAMA,GAJAhB,EAAAmD,UAAApD,EACA,sCAAAI,EAAAH,EAAAgB,MAGAhB,EAAAkB,IAAAnB,EACA,yBAAAI,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,UAFAD,CAGA,wBAAAI,EAHAJ,CAIA,gCACAuW,EAAAvW,EAAAC,EAAA,QACAuW,EAAAxW,EAAAC,EAAApK,EAAAuK,EAAA,SAAAoW,CACA,UAGA,GAAAvW,EAAAM,SAAA,CACA,IAAAa,EAAAhB,EACAH,EAAAoB,eACAD,EAAA,QAAAnB,EAAAqB,GACAtB,EAAA,SAAAoB,GACApB,EAAA,mEACAI,EAAAA,EAAAgB,EAAAhB,EAAAgB,EAAAhB,IAEAJ,EACA,yBAAAoB,EADApB,CAEA,WAAAsW,EAAArW,EAAA,SAFAD,CAGA,gCAAAoB,GACAnB,EAAAqD,cACAtD,EAAA,qCAAAoB,EAAA,OAEAoV,EAAAxW,EAAAC,EAAApK,EAAAuL,EAAA,OACAnB,EAAAqD,cACAtD,EAAA,KAEAA,EAAA,SAGA,CACA,GAAAC,EAAA4B,OAAA,CACA,IAAA4U,EAAA3W,EAAAoB,SAAAjB,EAAA4B,OAAAZ,MACA,IAAAoV,EAAApW,EAAA4B,OAAAZ,OAAAjB,EACA,cAAAyW,EADAzW,CAEA,WAAAC,EAAA4B,OAAAZ,KAAA,qBACAoV,EAAApW,EAAA4B,OAAAZ,MAAA,EACAjB,EACA,QAAAyW,GAEAD,EAAAxW,EAAAC,EAAApK,EAAAuK,GAEAH,EAAAmD,UAAApD,EACA,KAEA,OAAAA,EACA,gBAzLA,IAAAH,EAAA1L,EAAA,IACA2L,EAAA3L,EAAA,IAEA,SAAAmiB,EAAArW,EAAAyW,GACA,OAAAzW,EAAAgB,KAAA,KAAAyV,GAAAzW,EAAAM,UAAA,UAAAmW,EAAA,KAAAzW,EAAAkB,KAAA,WAAAuV,EAAA,MAAAzW,EAAA0C,QAAA,IAAA,IAAA,YAYA,SAAA6T,EAAAxW,EAAAC,EAAAC,EAAAE,GAEA,GAAAH,EAAAI,aACA,GAAAJ,EAAAI,wBAAAR,EAAA,CAAAG,EACA,cAAAI,EADAJ,CAEA,WAFAA,CAGA,WAAAsW,EAAArW,EAAA,eACA,IAAA,IAAAlI,EAAAD,OAAAC,KAAAkI,EAAAI,aAAAC,QAAAjK,EAAA,EAAAA,EAAA0B,EAAApD,SAAA0B,EAAA2J,EACA,WAAAC,EAAAI,aAAAC,OAAAvI,EAAA1B,KACA2J,EACA,QADAA,CAEA,UAEAA,EACA,IADAA,CAEA,8BAAAE,EAAAE,EAFAJ,CAGA,QAHAA,CAIA,aAAAC,EAAAgB,KAAA,IAJAjB,CAKA,UAGA,OAAAC,EAAAU,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAX,EACA,0BAAAI,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,YACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAI,EAAAA,EAAAA,EAAAA,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,iBACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAI,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,WACA,MACA,IAAA,OAAAD,EACA,4BAAAI,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,YACA,MACA,IAAA,SAAAD,EACA,yBAAAI,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,WACA,MACA,IAAA,QAAAD,EACA,4DAAAI,EAAAA,EAAAA,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,WAIA,OAAAD,EAYA,SAAAuW,EAAAvW,EAAAC,EAAAG,GAEA,OAAAH,EAAA0C,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAA3C,EACA,6BAAAI,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,gBACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAI,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,qBACA,MACA,IAAA,OAAAD,EACA,4BAAAI,EADAJ,CAEA,WAAAsW,EAAArW,EAAA,gBAGA,OAAAD,uCCzGA,IAAAgI,EAAA3T,EAEA0T,EAAA5T,EAAA,IA6BA6T,EAAA,wBAAA,CAEAnH,WAAA,SAAAqI,GAGA,GAAAA,GAAAA,EAAA,SAAA,CACA,IAAAvI,EAAA5H,KAAAiS,OAAA9B,EAAA,UAEA,GAAAvI,EAAA,CAEA,IAAAgW,EAAA,MAAAzN,EAAA,SAAA1T,OAAA,GACA0T,EAAA,SAAA0N,OAAA,GAAA1N,EAAA,SAEA,OAAAnQ,KAAAyK,OAAA,CACAmT,SAAA,IAAAA,EACAle,MAAAkI,EAAA7K,OAAA6K,EAAAE,WAAAqI,IAAA2F,YAKA,OAAA9V,KAAA8H,WAAAqI,IAGA3H,SAAA,SAAA6D,EAAAtL,GAGA,GAAAA,GAAAA,EAAAmK,MAAAmB,EAAAuR,UAAAvR,EAAA3M,MAAA,CAEA,IAAAwI,EAAAmE,EAAAuR,SAAAxH,UAAA/J,EAAAuR,SAAA1H,YAAA,KAAA,GACAtO,EAAA5H,KAAAiS,OAAA/J,GAEAN,IACAyE,EAAAzE,EAAA9J,OAAAuO,EAAA3M,QAIA,KAAA2M,aAAArM,KAAAyN,OAAApB,aAAA2C,EAAA,CACA,IAAAmB,EAAA9D,EAAAwD,MAAArH,SAAA6D,EAAAtL,GAEA,OADAoP,EAAA,SAAA9D,EAAAwD,MAAAnI,SACAyI,EAGA,OAAAnQ,KAAAwI,SAAA6D,EAAAtL,iCC/EA1F,EAAAC,QAAA+T,EAEA,IAEAC,EAFAvI,EAAA3L,EAAA,IAIAiY,EAAAtM,EAAAsM,SACAhX,EAAA0K,EAAA1K,OACAkK,EAAAQ,EAAAR,KAWA,SAAAuX,EAAAviB,EAAAiL,EAAAlE,GAMAtC,KAAAzE,GAAAA,EAMAyE,KAAAwG,IAAAA,EAMAxG,KAAA+d,KAAAjjB,EAMAkF,KAAAsC,IAAAA,EAIA,SAAA0b,KAUA,SAAAC,EAAAnO,GAMA9P,KAAAke,KAAApO,EAAAoO,KAMAle,KAAAme,KAAArO,EAAAqO,KAMAne,KAAAwG,IAAAsJ,EAAAtJ,IAMAxG,KAAA+d,KAAAjO,EAAAsO,OAQA,SAAA/O,IAMArP,KAAAwG,IAAA,EAMAxG,KAAAke,KAAA,IAAAJ,EAAAE,EAAA,EAAA,GAMAhe,KAAAme,KAAAne,KAAAke,KAMAle,KAAAoe,OAAA,KAqDA,SAAAC,EAAA/b,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EAoBA,SAAAgc,EAAA9X,EAAAlE,GACAtC,KAAAwG,IAAAA,EACAxG,KAAA+d,KAAAjjB,EACAkF,KAAAsC,IAAAA,EA8CA,SAAAic,EAAAjc,EAAAC,EAAAC,GACA,KAAAF,EAAA4C,IACA3C,EAAAC,KAAA,IAAAF,EAAA2C,GAAA,IACA3C,EAAA2C,IAAA3C,EAAA2C,KAAA,EAAA3C,EAAA4C,IAAA,MAAA,EACA5C,EAAA4C,MAAA,EAEA,KAAA,IAAA5C,EAAA2C,IACA1C,EAAAC,KAAA,IAAAF,EAAA2C,GAAA,IACA3C,EAAA2C,GAAA3C,EAAA2C,KAAA,EAEA1C,EAAAC,KAAAF,EAAA2C,GA2CA,SAAAuZ,EAAAlc,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAtKA+M,EAAA5E,OAAA1D,EAAA+M,OACA,WACA,OAAAzE,EAAA5E,OAAA,WACA,OAAA,IAAA6E,OAIA,WACA,OAAA,IAAAD,GAQAA,EAAApJ,MAAA,SAAAC,GACA,OAAA,IAAAa,EAAArL,MAAAwK,IAKAa,EAAArL,QAAAA,QACA2T,EAAApJ,MAAAc,EAAA0U,KAAApM,EAAApJ,MAAAc,EAAArL,MAAAwE,UAAA+T,WAUA5E,EAAAnP,UAAAue,EAAA,SAAAljB,EAAAiL,EAAAlE,GAGA,OAFAtC,KAAAme,KAAAne,KAAAme,KAAAJ,KAAA,IAAAD,EAAAviB,EAAAiL,EAAAlE,GACAtC,KAAAwG,KAAAA,EACAxG,OA8BAse,EAAApe,UAAAnB,OAAA0L,OAAAqT,EAAA5d,YACA3E,GAxBA,SAAA+G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,KAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,GA0BA+M,EAAAnP,UAAAgU,OAAA,SAAAxU,GAWA,OARAM,KAAAwG,MAAAxG,KAAAme,KAAAne,KAAAme,KAAAJ,KAAA,IAAAO,GACA5e,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,IAAA8G,IACAxG,MASAqP,EAAAnP,UAAAiU,MAAA,SAAAzU,GACA,OAAAA,EAAA,EACAM,KAAAye,EAAAF,EAAA,GAAAlL,EAAAjG,WAAA1N,IACAM,KAAAkU,OAAAxU,IAQA2P,EAAAnP,UAAAkU,OAAA,SAAA1U,GACA,OAAAM,KAAAkU,QAAAxU,GAAA,EAAAA,GAAA,MAAA,IAkCA2P,EAAAnP,UAAA4U,MAZAzF,EAAAnP,UAAA6U,OAAA,SAAArV,GACA,IAAAiU,EAAAN,EAAAmH,KAAA9a,GACA,OAAAM,KAAAye,EAAAF,EAAA5K,EAAA/X,SAAA+X,IAkBAtE,EAAAnP,UAAA8U,OAAA,SAAAtV,GACA,IAAAiU,EAAAN,EAAAmH,KAAA9a,GAAA4a,WACA,OAAAta,KAAAye,EAAAF,EAAA5K,EAAA/X,SAAA+X,IAQAtE,EAAAnP,UAAAmU,KAAA,SAAA3U,GACA,OAAAM,KAAAye,EAAAJ,EAAA,EAAA3e,EAAA,EAAA,IAyBA2P,EAAAnP,UAAAqU,SAVAlF,EAAAnP,UAAAoU,QAAA,SAAA5U,GACA,OAAAM,KAAAye,EAAAD,EAAA,EAAA9e,IAAA,IA6BA2P,EAAAnP,UAAAiV,SAZA9F,EAAAnP,UAAAgV,QAAA,SAAAxV,GACA,IAAAiU,EAAAN,EAAAmH,KAAA9a,GACA,OAAAM,KAAAye,EAAAD,EAAA,EAAA7K,EAAA1O,IAAAwZ,EAAAD,EAAA,EAAA7K,EAAAzO,KAkBAmK,EAAAnP,UAAAsU,MAAA,SAAA9U,GACA,OAAAM,KAAAye,EAAA1X,EAAAyN,MAAA5R,aAAA,EAAAlD,IASA2P,EAAAnP,UAAAuU,OAAA,SAAA/U,GACA,OAAAM,KAAAye,EAAA1X,EAAAyN,MAAA/P,cAAA,EAAA/E,IAGA,IAAAgf,EAAA3X,EAAArL,MAAAwE,UAAAiT,IACA,SAAA7Q,EAAAC,EAAAC,GACAD,EAAA4Q,IAAA7Q,EAAAE,IAGA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAA1F,EAAA,EAAAA,EAAAwF,EAAA1G,SAAAkB,EACAyF,EAAAC,EAAA1F,GAAAwF,EAAAxF,IAQAuS,EAAAnP,UAAAmJ,MAAA,SAAA3J,GACA,IAAA8G,EAAA9G,EAAA9D,SAAA,EACA,IAAA4K,EACA,OAAAxG,KAAAye,EAAAJ,EAAA,EAAA,GACA,GAAAtX,EAAAyE,SAAA9L,GAAA,CACA,IAAA6C,EAAA8M,EAAApJ,MAAAO,EAAAnK,EAAAT,OAAA8D,IACArD,EAAAyB,OAAA4B,EAAA6C,EAAA,GACA7C,EAAA6C,EAEA,OAAAvC,KAAAkU,OAAA1N,GAAAiY,EAAAC,EAAAlY,EAAA9G,IAQA2P,EAAAnP,UAAA5D,OAAA,SAAAoD,GACA,IAAA8G,EAAAD,EAAA3K,OAAA8D,GACA,OAAA8G,EACAxG,KAAAkU,OAAA1N,GAAAiY,EAAAlY,EAAAG,MAAAF,EAAA9G,GACAM,KAAAye,EAAAJ,EAAA,EAAA,IAQAhP,EAAAnP,UAAAkZ,KAAA,WAIA,OAHApZ,KAAAoe,OAAA,IAAAH,EAAAje,MACAA,KAAAke,KAAAle,KAAAme,KAAA,IAAAL,EAAAE,EAAA,EAAA,GACAhe,KAAAwG,IAAA,EACAxG,MAOAqP,EAAAnP,UAAAye,MAAA,WAUA,OATA3e,KAAAoe,QACApe,KAAAke,KAAAle,KAAAoe,OAAAF,KACAle,KAAAme,KAAAne,KAAAoe,OAAAD,KACAne,KAAAwG,IAAAxG,KAAAoe,OAAA5X,IACAxG,KAAAoe,OAAApe,KAAAoe,OAAAL,OAEA/d,KAAAke,KAAAle,KAAAme,KAAA,IAAAL,EAAAE,EAAA,EAAA,GACAhe,KAAAwG,IAAA,GAEAxG,MAOAqP,EAAAnP,UAAAmZ,OAAA,WACA,IAAA6E,EAAAle,KAAAke,KACAC,EAAAne,KAAAme,KACA3X,EAAAxG,KAAAwG,IAOA,OANAxG,KAAA2e,QAAAzK,OAAA1N,GACAA,IACAxG,KAAAme,KAAAJ,KAAAG,EAAAH,KACA/d,KAAAme,KAAAA,EACAne,KAAAwG,KAAAA,GAEAxG,MAOAqP,EAAAnP,UAAA4V,OAAA,WAIA,IAHA,IAAAoI,EAAAle,KAAAke,KAAAH,KACAxb,EAAAvC,KAAA0K,YAAAzE,MAAAjG,KAAAwG,KACAhE,EAAA,EACA0b,GACAA,EAAA3iB,GAAA2iB,EAAA5b,IAAAC,EAAAC,GACAA,GAAA0b,EAAA1X,IACA0X,EAAAA,EAAAH,KAGA,OAAAxb,GAGA8M,EAAAnB,EAAA,SAAA0Q,GACAtP,EAAAsP,+BCxcAvjB,EAAAC,QAAAgU,EAGA,IAAAD,EAAAjU,EAAA,KACAkU,EAAApP,UAAAnB,OAAA0L,OAAA4E,EAAAnP,YAAAwK,YAAA4E,EAEA,IAAAvI,EAAA3L,EAAA,IAEA0Y,EAAA/M,EAAA+M,OAQA,SAAAxE,IACAD,EAAA/I,KAAAtG,MAQAsP,EAAArJ,MAAA,SAAAC,GACA,OAAAoJ,EAAArJ,MAAAc,EAAAsV,GAAAnW,IAGA,IAAA2Y,EAAA/K,GAAAA,EAAA5T,qBAAAyB,YAAA,QAAAmS,EAAA5T,UAAAiT,IAAAjL,KACA,SAAA5F,EAAAC,EAAAC,GACAD,EAAA4Q,IAAA7Q,EAAAE,IAIA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAAwc,KACAxc,EAAAwc,KAAAvc,EAAAC,EAAA,EAAAF,EAAA1G,aACA,IAAA,IAAAkB,EAAA,EAAAA,EAAAwF,EAAA1G,QACA2G,EAAAC,KAAAF,EAAAxF,MAgBA,SAAAiiB,EAAAzc,EAAAC,EAAAC,GACAF,EAAA1G,OAAA,GACAmL,EAAAR,KAAAG,MAAApE,EAAAC,EAAAC,GAEAD,EAAA4Z,UAAA7Z,EAAAE,GAdA8M,EAAApP,UAAAmJ,MAAA,SAAA3J,GACAqH,EAAAyE,SAAA9L,KACAA,EAAAqH,EAAAqV,EAAA1c,EAAA,WACA,IAAA8G,EAAA9G,EAAA9D,SAAA,EAIA,OAHAoE,KAAAkU,OAAA1N,GACAA,GACAxG,KAAAye,EAAAI,EAAArY,EAAA9G,GACAM,MAaAsP,EAAApP,UAAA5D,OAAA,SAAAoD,GACA,IAAA8G,EAAAsN,EAAAkL,WAAAtf,GAIA,OAHAM,KAAAkU,OAAA1N,GACAA,GACAxG,KAAAye,EAAAM,EAAAvY,EAAA9G,GACAM,uBvCvEAhF,KAAAC,OAcAC,EAPA,SAAA+jB,EAAA/W,GACA,IAAAgX,EAAAlkB,EAAAkN,GAGA,OAFAgX,GACAnkB,EAAAmN,GAAA,GAAA5B,KAAA4Y,EAAAlkB,EAAAkN,GAAA,CAAA5M,QAAA,IAAA2jB,EAAAC,EAAAA,EAAA5jB,SACA4jB,EAAA5jB,QAGA2jB,CAAAhkB,EAAA,IAGAC,EAAA6L,KAAA2U,OAAAxgB,SAAAA,EAGA,mBAAA0W,QAAAA,OAAAuN,KACAvN,OAAA,CAAA,QAAA,SAAArF,GAKA,OAJAA,GAAAA,EAAA6S,SACAlkB,EAAA6L,KAAAwF,KAAAA,EACArR,EAAAgU,aAEAhU,IAIA,iBAAAG,QAAAA,QAAAA,OAAAC,UACAD,OAAAC,QAAAJ,GA/BA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\n\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\n    // sources through a conflict-free require shim and is again wrapped within an iife that\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\n    // so that minification can remove the directives of each module.\n\n    function $require(name) {\n        var $module = cache[name];\n        if (!$module)\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\n        return $module.exports;\n    }\n\n    var protobuf = $require(entries[0]);\n\n    // Expose globally\n    protobuf.util.global.protobuf = protobuf;\n\n    // Be nice to AMD\n    if (typeof define === \"function\" && define.amd)\n        define([\"long\"], function(Long) {\n            if (Long && Long.isLong) {\n                protobuf.util.Long = Long;\n                protobuf.configure();\n            }\n            return protobuf;\n        });\n\n    // Be nice to CommonJS\n    if (typeof module === \"object\" && module && module.exports)\n        module.exports = protobuf;\n\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\n/**\n * Runtime message from/to plain object converters.\n * @namespace\n */\nvar converter = exports;\n\nvar Enum = require(14),\n    util = require(33);\n\n/**\n * Generates a partial value fromObject conveter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop, ref) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (ref === undefined) {\n      ref = \"d\" + prop;\n    }\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref);\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\n                if (field.repeated && values[keys[i]] === field.typeDefault) gen\n                (\"default:\");\n                gen\n                (\"case%j:\", keys[i])\n                (\"case %i:\", values[keys[i]])\n                    (\"m%s=%j\", prop, values[keys[i]])\n                    (\"break\");\n            } gen\n            (\"}\");\n        } else gen\n            (\"if(typeof %s!==\\\"object\\\")\", ref)\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n            (\"m%s=types[%i].fromObject(%s)\", prop, fieldIndex, ref);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n                (\"m%s=Number(%s)\", prop, ref); // also catches \"NaN\", \"Infinity\"\n                break;\n            case \"uint32\":\n            case \"fixed32\": gen\n                (\"m%s=%s>>>0\", prop, ref);\n                break;\n            case \"int32\":\n            case \"sint32\":\n            case \"sfixed32\": gen\n                (\"m%s=%s|0\", prop, ref);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(util.Long)\")\n                    (\"(m%s=util.Long.fromValue(%s)).unsigned=%j\", prop, ref, isUnsigned)\n                (\"else if(typeof %s===\\\"string\\\")\", ref)\n                    (\"m%s=parseInt(%s,10)\", prop, ref)\n                (\"else if(typeof %s===\\\"number\\\")\", ref)\n                    (\"m%s=%s\", prop, ref)\n                (\"else if(typeof %s===\\\"object\\\")\", ref)\n                    (\"m%s=new util.LongBits(%s.low>>>0,%s.high>>>0).toNumber(%s)\", prop, ref, ref, isUnsigned ? \"true\" : \"\");\n                break;\n            case \"bytes\": gen\n                (\"if(typeof %s===\\\"string\\\")\", ref)\n                    (\"util.base64.decode(%s,m%s=util.newBuffer(util.base64.length(%s)),0)\", ref, prop, ref)\n                (\"else if(%s.length)\", ref)\n                    (\"m%s=%s\", prop, ref);\n                break;\n            case \"string\": gen\n                (\"m%s=String(%s)\", prop, ref);\n                break;\n            case \"bool\": gen\n                (\"m%s=Boolean(%s)\", prop, ref);\n                break;\n            /* default: gen\n                (\"m%s=%s\", prop, ref);\n                break; */\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a plain object to runtime message converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.fromObject = function fromObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray;\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\n    (\"if(d instanceof this.ctor)\")\n        (\"return d\");\n    if (!fields.length) return gen\n    (\"return new this.ctor\");\n    gen\n    (\"var m=new this.ctor\");\n    for (var i = 0; i < fields.length; ++i) {\n        var field  = fields[i].resolve(),\n            prop   = util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) { gen\n    (\"if(d%s){\", prop)\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n        (\"m%s={}\", prop)\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\n        (\"}\")\n    (\"}\");\n\n        // Repeated fields\n        } else if (field.repeated) {\n          gen(\"if(d%s){\", prop);\n          var arrayRef = \"d\" + prop;\n          if (field.useToArray()) {\n            arrayRef = \"array\" + field.id;\n            gen(\"var %s\", arrayRef);\n            gen(\"if (d%s!=null&&d%s.toArray) { %s = d%s.toArray() } else { %s = d%s }\",\n                prop, prop, arrayRef, prop, arrayRef, prop);\n          }\n          gen\n        (\"if(!Array.isArray(%s))\", arrayRef)\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\n        (\"m%s=[]\", prop)\n        (\"for(var i=0;i<%s.length;++i){\", arrayRef);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\", arrayRef + \"[i]\")\n        (\"}\")\n    (\"}\");\n\n        // Non-repeated fields\n        } else {\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\n            if (!(field.resolvedType instanceof Enum)) gen\n    (\"}\");\n        }\n    } return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n\n/**\n * Generates a partial value toObject converter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) gen\n            (\"d%s=o.enums===String?types[%i].values[m%s]:m%s\", prop, fieldIndex, prop, prop);\n        else gen\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\n            (\"else\") // Long-like\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\n                break;\n            case \"bytes\": gen\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\n                break;\n            default: gen\n            (\"d%s=m%s\", prop, prop);\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a runtime message to plain object converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.toObject = function toObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\n    if (!fields.length)\n        return util.codegen()(\"return {}\");\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\n    (\"if(!o)\")\n        (\"o={}\")\n    (\"var d={}\");\n\n    var repeatedFields = [],\n        mapFields = [],\n        normalFields = [],\n        i = 0;\n    for (; i < fields.length; ++i)\n        if (!fields[i].partOf)\n            ( fields[i].resolve().repeated ? repeatedFields\n            : fields[i].map ? mapFields\n            : normalFields).push(fields[i]);\n\n    if (repeatedFields.length) { gen\n    (\"if(o.arrays||o.defaults){\");\n        for (i = 0; i < repeatedFields.length; ++i) gen\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (mapFields.length) { gen\n    (\"if(o.objects||o.defaults){\");\n        for (i = 0; i < mapFields.length; ++i) gen\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (normalFields.length) { gen\n    (\"if(o.defaults){\");\n        for (i = 0; i < normalFields.length; ++i) {\n            var field = normalFields[i],\n                prop  = util.safeProp(field.name);\n            if (field.resolvedType instanceof Enum) gen\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\n            else if (field.long) gen\n        (\"if(util.Long){\")\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\n        (\"}else\")\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\n            else if (field.bytes) {\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\n                gen\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\n        (\"else{\")\n            (\"d%s=%s\", prop, arrayDefault)\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\n        (\"}\");\n            } else gen\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\n        } gen\n    (\"}\");\n    }\n    var hasKs2 = false;\n    for (i = 0; i < fields.length; ++i) {\n        var field = fields[i],\n            index = mtype._fieldsArray.indexOf(field),\n            prop  = util.safeProp(field.name);\n        if (field.map) {\n            if (!hasKs2) { hasKs2 = true; gen\n    (\"var ks2\");\n            } gen\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\n        (\"d%s={}\", prop)\n        (\"for(var j=0;j<ks2.length;++j){\");\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\n        (\"}\");\n        } else if (field.repeated) { gen\n    (\"if(m%s&&m%s.length){\", prop, prop)\n        (\"d%s=[]\", prop)\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\n        (\"}\");\n        } else { gen\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\n        if (field.partOf) gen\n        (\"if(o.oneofs)\")\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\n        }\n        gen\n    (\"}\");\n    }\n    return gen\n    (\"return d\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n", "\"use strict\";\nmodule.exports = decoder;\n\nvar Enum    = require(14),\n    types   = require(32),\n    util    = require(33);\n\nfunction missing(field) {\n    return \"missing required '\" + field.name + \"'\";\n}\n\n/**\n * Generates a decoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction decoder(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"r\", \"l\"], mtype.name + \"$decode\")\n    (\"if(!(r instanceof Reader))\")\n        (\"r=Reader.create(r)\")\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k\" : \"\"))\n    (\"while(r.pos<c){\")\n        (\"var t=r.uint32()\");\n    if (mtype.group) gen\n        (\"if((t&7)===4)\")\n            (\"break\");\n    gen\n        (\"switch(t>>>3){\");\n\n    var i = 0;\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            ref   = \"m\" + util.safeProp(field.name); gen\n            (\"case %i:\", field.id);\n\n        // Map fields\n        if (field.map) { gen\n                (\"r.skip().pos++\") // assumes id 1 + key wireType\n                (\"if(%s===util.emptyObject)\", ref)\n                    (\"%s={}\", ref)\n                (\"k=r.%s()\", field.keyType)\n                (\"r.pos++\"); // assumes id 2 + value wireType\n            if (types.long[field.keyType] !== undefined) {\n                if (types.basic[type] === undefined) gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=types[%i].decode(r,r.uint32())\", ref, i); // can't be groups\n                else gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=r.%s()\", ref, type);\n            } else {\n                if (types.basic[type] === undefined) gen\n                (\"%s[k]=types[%i].decode(r,r.uint32())\", ref, i); // can't be groups\n                else gen\n                (\"%s[k]=r.%s()\", ref, type);\n            }\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n\n                (\"if(!(%s&&%s.length))\", ref, ref)\n                    (\"%s=[]\", ref);\n\n            // Packable (always check for forward and backward compatiblity)\n            if (types.packed[type] !== undefined) gen\n                (\"if((t&7)===2){\")\n                    (\"var c2=r.uint32()+r.pos\")\n                    (\"while(r.pos<c2)\")\n                        (\"%s.push(r.%s())\", ref, type)\n                (\"}else\");\n\n            // Non-packed\n            if (types.basic[type] === undefined) gen(field.resolvedType.group\n                    ? \"%s.push(types[%i].decode(r))\"\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\n            else gen\n                    (\"%s.push(r.%s())\", ref, type);\n\n        // Non-repeated\n        } else if (types.basic[type] === undefined) gen(field.resolvedType.group\n                ? \"%s=types[%i].decode(r)\"\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\n        else gen\n                (\"%s=r.%s()\", ref, type);\n        gen\n                (\"break\");\n    // Unknown fields\n    } gen\n            (\"default:\")\n                (\"r.skipType(t&7)\")\n                (\"break\")\n\n        (\"}\")\n    (\"}\");\n\n    // Field presence\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\n        var rfield = mtype._fieldsArray[i];\n        if (rfield.required) gen\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\n    }\n\n    return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline */\n}\n", "\"use strict\";\nmodule.exports = encoder;\n\nvar Enum     = require(14),\n    types    = require(32),\n    util     = require(33);\n\n/**\n * Generates a partial message type encoder.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genTypePartial(gen, field, fieldIndex, ref) {\n  /* eslint-disable no-unexpected-multiline */\n  if (field.resolvedType.group) {\n    gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0);\n    return;\n  }\n  var key = (field.id << 3 | 2) >>> 0;\n  if (field.preEncoded()) {\n    gen(\"if (%s instanceof Uint8Array) {\", ref)\n    (\"w.uint32(%i)\", key)\n    (\"w.bytes(%s)\", ref)\n    (\"} else {\");\n  }\n  gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, key);\n  if (field.preEncoded()) {\n    gen(\"}\")\n  }\n}\n\n/**\n * Generates an encoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction encoder(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\n    (\"if(!w)\")\n        (\"w=Writer.create()\");\n\n    var i, ref;\n\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\n\n    for (var i = 0; i < fields.length; ++i) {\n        var field    = fields[i].resolve(),\n            index    = mtype._fieldsArray.indexOf(field),\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            wireType = types.basic[type];\n            ref      = \"m\" + util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) {\n            gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j)){\", ref, field.name) // !== undefined && !== null\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\n            if (wireType === undefined) gen\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\n            else gen\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\n            gen\n        (\"}\")\n    (\"}\");\n\n            // Repeated fields\n        } else if (field.repeated) {\n          var arrayRef = ref;\n          if (field.useToArray()) {\n            arrayRef = \"array\" + field.id;\n            gen(\"var %s\", arrayRef);\n            gen(\"if (%s!=null&&%s.toArray) { %s = %s.toArray() } else { %s = %s }\",\n                ref, ref, arrayRef, ref, arrayRef, ref);\n          }\n          gen(\"if(%s!=null&&%s.length){\", arrayRef, arrayRef); // !== undefined && !== null\n            // Packed repeated\n            if (field.packed && types.packed[type] !== undefined) { gen\n\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\n        (\"for(var i=0;i<%s.length;++i)\", arrayRef)\n            (\"w.%s(%s[i])\", type, arrayRef)\n        (\"w.ldelim()\");\n\n            // Non-packed\n            } else { gen\n\n        (\"for(var i=0;i<%s.length;++i)\", arrayRef);\n                if (wireType === undefined)\n            genTypePartial(gen, field, index, arrayRef + \"[i]\");\n                else gen\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, arrayRef);\n\n            } gen\n    (\"}\");\n\n        // Non-repeated\n        } else {\n            if (field.optional) gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j))\", ref, field.name); // !== undefined && !== null\n\n            if (wireType === undefined)\n        genTypePartial(gen, field, index, ref);\n            else gen\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n        }\n    }\n\n    return gen\n    (\"return w\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n", "\"use strict\";\nmodule.exports = Enum;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\n\nvar Namespace = require(21),\n    util = require(33);\n\n/**\n * Constructs a new enum instance.\n * @classdesc Reflected enum.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {Object.<string,number>} [values] Enum values as an object, by name\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this enum\n * @param {Object.<string,string>} [comments] The value comments for this enum\n */\nfunction Enum(name, values, options, comment, comments) {\n    ReflectionObject.call(this, name, options);\n\n    if (values && typeof values !== \"object\")\n        throw TypeError(\"values must be an object\");\n\n    /**\n     * Enum values by id.\n     * @type {Object.<number,string>}\n     */\n    this.valuesById = {};\n\n    /**\n     * Enum values by name.\n     * @type {Object.<string,number>}\n     */\n    this.values = Object.create(this.valuesById); // toJSON, marker\n\n    /**\n     * Enum comment text.\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Value comment texts, if any.\n     * @type {Object.<string,string>}\n     */\n    this.comments = comments || {};\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\n    // static and reflection code alike instead of emitting generic object definitions.\n\n    if (values)\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\n}\n\n/**\n * Enum descriptor.\n * @interface IEnum\n * @property {Object.<string,number>} values Enum values\n * @property {Object.<string,*>} [options] Enum options\n */\n\n/**\n * Constructs an enum from an enum descriptor.\n * @param {string} name Enum name\n * @param {IEnum} json Enum descriptor\n * @returns {Enum} Created enum\n * @throws {TypeError} If arguments are invalid\n */\nEnum.fromJSON = function fromJSON(name, json) {\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\n    enm.reserved = json.reserved;\n    return enm;\n};\n\n/**\n * Converts this enum to an enum descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IEnum} Enum descriptor\n */\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"  , this.options,\n        \"values\"   , this.values,\n        \"reserved\" , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"comment\"  , keepComments ? this.comment : undefined,\n        \"comments\" , keepComments ? this.comments : undefined\n    ]);\n};\n\n/**\n * Adds a value to this enum.\n * @param {string} name Value name\n * @param {number} id Value id\n * @param {string} [comment] Comment, if any\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a value with this name or id\n */\nEnum.prototype.add = function add(name, id, comment) {\n    // utilized by the parser but not by .fromJSON\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (!util.isInteger(id))\n        throw TypeError(\"id must be an integer\");\n\n    if (this.values[name] !== undefined)\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\n\n    if (this.isReservedId(id))\n        throw Error(\"id \" + id + \" is reserved in \" + this);\n\n    if (this.isReservedName(name))\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\n\n    if (this.valuesById[id] !== undefined) {\n        if (!(this.options && this.options.allow_alias))\n            throw Error(\"duplicate id \" + id + \" in \" + this);\n        this.values[name] = id;\n    } else\n        this.valuesById[this.values[name] = id] = name;\n\n    this.comments[name] = comment || null;\n    return this;\n};\n\n/**\n * Removes a value from this enum\n * @param {string} name Value name\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `name` is not a name of this enum\n */\nEnum.prototype.remove = function remove(name) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    var val = this.values[name];\n    if (val == null)\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\n\n    delete this.valuesById[val];\n    delete this.values[name];\n    delete this.comments[name];\n\n    return this;\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n", "\"use strict\";\nmodule.exports = Field;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\n\nvar Enum  = require(14),\n    types = require(32),\n    util  = require(33);\n\nvar Type; // cyclic\n\nvar ruleRe = /^required|optional|repeated$/;\n\n/**\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\n * @name Field\n * @classdesc Reflected message field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a field from a field descriptor.\n * @param {string} name Field name\n * @param {IField} json Field descriptor\n * @returns {Field} Created field\n * @throws {TypeError} If arguments are invalid\n */\nField.fromJSON = function fromJSON(name, json) {\n    return new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\n};\n\n/**\n * Not an actual constructor. Use {@link Field} instead.\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports FieldBase\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction Field(name, id, type, rule, extend, options, comment) {\n\n    if (util.isObject(rule)) {\n        comment = extend;\n        options = rule;\n        rule = extend = undefined;\n    } else if (util.isObject(extend)) {\n        comment = options;\n        options = extend;\n        extend = undefined;\n    }\n\n    ReflectionObject.call(this, name, options);\n\n    if (!util.isInteger(id) || id < 0)\n        throw TypeError(\"id must be a non-negative integer\");\n\n    if (!util.isString(type))\n        throw TypeError(\"type must be a string\");\n\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\n        throw TypeError(\"rule must be a string rule\");\n\n    if (extend !== undefined && !util.isString(extend))\n        throw TypeError(\"extend must be a string\");\n\n    /**\n     * Field rule, if any.\n     * @type {string|undefined}\n     */\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\n\n    /**\n     * Field type.\n     * @type {string}\n     */\n    this.type = type; // toJSON\n\n    /**\n     * Unique field id.\n     * @type {number}\n     */\n    this.id = id; // toJSON, marker\n\n    /**\n     * Extended type if different from parent.\n     * @type {string|undefined}\n     */\n    this.extend = extend || undefined; // toJSON\n\n    /**\n     * Whether this field is required.\n     * @type {boolean}\n     */\n    this.required = rule === \"required\";\n\n    /**\n     * Whether this field is optional.\n     * @type {boolean}\n     */\n    this.optional = !this.required;\n\n    /**\n     * Whether this field is repeated.\n     * @type {boolean}\n     */\n    this.repeated = rule === \"repeated\";\n\n    /**\n     * Whether this field is a map or not.\n     * @type {boolean}\n     */\n    this.map = false;\n\n    /**\n     * Message this field belongs to.\n     * @type {Type|null}\n     */\n    this.message = null;\n\n    /**\n     * OneOf this field belongs to, if any,\n     * @type {OneOf|null}\n     */\n    this.partOf = null;\n\n    /**\n     * The field type's default value.\n     * @type {*}\n     */\n    this.typeDefault = null;\n\n    /**\n     * The field's default value on prototypes.\n     * @type {*}\n     */\n    this.defaultValue = null;\n\n    /**\n     * Whether this field's value should be treated as a long.\n     * @type {boolean}\n     */\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\n\n    /**\n     * Whether this field's value is a buffer.\n     * @type {boolean}\n     */\n    this.bytes = type === \"bytes\";\n\n    /**\n     * Resolved type if not a basic type.\n     * @type {Type|Enum|null}\n     */\n    this.resolvedType = null;\n\n    /**\n     * Sister-field within the extended type if a declaring extension field.\n     * @type {Field|null}\n     */\n    this.extensionField = null;\n\n    /**\n     * Sister-field within the declaring namespace if an extended field.\n     * @type {Field|null}\n     */\n    this.declaringField = null;\n\n    /**\n     * Internally remembers whether this field is packed.\n     * @type {boolean|null}\n     * @private\n     */\n    this._packed = null;\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Determines whether this field is packed. Only relevant when repeated and working with proto2.\n * @name Field#packed\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"packed\", {\n    get: function() {\n        // defaults to packed=true if not explicity set to false\n        if (this._packed === null)\n            this._packed = this.getOption(\"packed\") !== false;\n        return this._packed;\n    }\n});\n\n/**\n * @override\n */\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (name === \"packed\") // clear cached before setting\n        this._packed = null;\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\n};\n\n/**\n * Field descriptor.\n * @interface IField\n * @property {string} [rule=\"optional\"] Field rule\n * @property {string} type Field type\n * @property {number} id Field id\n * @property {Object.<string,*>} [options] Field options\n */\n\n/**\n * Extension field descriptor.\n * @interface IExtensionField\n * @extends IField\n * @property {string} extend Extended type\n */\n\n/**\n * Converts this field to a field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IField} Field descriptor\n */\nField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Resolves this field's type references.\n * @returns {Field} `this`\n * @throws {Error} If any reference cannot be resolved\n */\nField.prototype.resolve = function resolve() {\n\n    if (this.resolved)\n        return this;\n\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\n        if (this.resolvedType instanceof Type)\n            this.typeDefault = null;\n        else // instanceof Enum\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\n    }\n\n    // use explicitly set default value if present\n    if (this.options && this.options[\"default\"] != null) {\n        this.typeDefault = this.options[\"default\"];\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\n    }\n\n    // remove unnecessary options\n    if (this.options) {\n        if (this.options.packed === true || this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\n            delete this.options.packed;\n        if (!Object.keys(this.options).length)\n            this.options = undefined;\n    }\n\n    // convert to internal data type if necesssary\n    if (this.long) {\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\n\n        /* istanbul ignore else */\n        if (Object.freeze)\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\n\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\n        var buf;\n        if (util.base64.test(this.typeDefault))\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\n        else\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\n        this.typeDefault = buf;\n    }\n\n    // take special care of maps and repeated fields\n    if (this.map)\n        this.defaultValue = util.emptyObject;\n    else if (this.repeated)\n        this.defaultValue = util.emptyArray;\n    else\n        this.defaultValue = this.typeDefault;\n\n    // ensure proper value on prototype\n    if (this.parent instanceof Type)\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n\nField.prototype.useToArray = function useToArray() {\n    return !!this.getOption(\"(js_use_toArray)\");\n};\n\nField.prototype.preEncoded = function preEncoded() {\n    return !!this.getOption(\"(js_preEncoded)\");\n};\n\n/**\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\n * @typedef FieldDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} fieldName Field name\n * @returns {undefined}\n */\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @param {T} [defaultValue] Default value\n * @returns {FieldDecorator} Decorator function\n * @template T extends number | number[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\n */\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\n\n    // submessage: decorate the submessage and use its name as the type\n    if (typeof fieldType === \"function\")\n        fieldType = util.decorateType(fieldType).name;\n\n    // enum reference: create a reflected copy of the enum and keep reuseing it\n    else if (fieldType && typeof fieldType === \"object\")\n        fieldType = util.decorateEnum(fieldType).name;\n\n    return function fieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\n    };\n};\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {Constructor<T>|string} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @returns {FieldDecorator} Decorator function\n * @template T extends Message<T>\n * @variation 2\n */\n// like Field.d but without a default value\n\n// Sets up cyclic dependencies (called in index-light)\nField._configure = function configure(Type_) {\n    Type = Type_;\n};\n", "\"use strict\";\nvar protobuf = module.exports = require(17);\n\nprotobuf.build = \"light\";\n\n/**\n * A node-style callback as used by {@link load} and {@link Root#load}.\n * @typedef LoadCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Root} [root] Root, if there hasn't been an error\n * @returns {undefined}\n */\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n */\nfunction load(filename, root, callback) {\n    if (typeof root === \"function\") {\n        callback = root;\n        root = new protobuf.Root();\n    } else if (!root)\n        root = new protobuf.Root();\n    return root.load(filename, callback);\n}\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Promise<Root>} Promise\n * @see {@link Root#load}\n * @variation 3\n */\n// function load(filename:string, [root:Root]):Promise<Root>\n\nprotobuf.load = load;\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n * @see {@link Root#loadSync}\n */\nfunction loadSync(filename, root) {\n    if (!root)\n        root = new protobuf.Root();\n    return root.loadSync(filename);\n}\n\nprotobuf.loadSync = loadSync;\n\n// Serialization\nprotobuf.encoder          = require(13);\nprotobuf.decoder          = require(12);\nprotobuf.verifier         = require(36);\nprotobuf.converter        = require(11);\n\n// Reflection\nprotobuf.ReflectionObject = require(22);\nprotobuf.Namespace        = require(21);\nprotobuf.Root             = require(26);\nprotobuf.Enum             = require(14);\nprotobuf.Type             = require(31);\nprotobuf.Field            = require(15);\nprotobuf.OneOf            = require(23);\nprotobuf.MapField         = require(18);\nprotobuf.Service          = require(30);\nprotobuf.Method           = require(20);\n\n// Runtime\nprotobuf.Message          = require(19);\nprotobuf.wrappers         = require(37);\n\n// Utility\nprotobuf.types            = require(32);\nprotobuf.util             = require(33);\n\n// Set up possibly cyclic reflection dependencies\nprotobuf.ReflectionObject._configure(protobuf.Root);\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\nprotobuf.Root._configure(protobuf.Type);\nprotobuf.Field._configure(protobuf.Type);\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(38);\nprotobuf.BufferWriter = require(39);\nprotobuf.Reader       = require(24);\nprotobuf.BufferReader = require(25);\n\n// Utility\nprotobuf.util         = require(35);\nprotobuf.rpc          = require(28);\nprotobuf.roots        = require(27);\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.Reader._configure(protobuf.BufferReader);\n    protobuf.util._configure();\n}\n\n// Set up buffer utility according to the environment\nprotobuf.Writer._configure(protobuf.BufferWriter);\nconfigure();\n", "\"use strict\";\nmodule.exports = MapField;\n\n// extends Field\nvar Field = require(15);\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\n\nvar types   = require(32),\n    util    = require(33);\n\n/**\n * Constructs a new map field instance.\n * @classdesc Reflected map field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} keyType Key type\n * @param {string} type Value type\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction MapField(name, id, keyType, type, options, comment) {\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\n\n    /* istanbul ignore if */\n    if (!util.isString(keyType))\n        throw TypeError(\"keyType must be a string\");\n\n    /**\n     * Key type.\n     * @type {string}\n     */\n    this.keyType = keyType; // toJSON, marker\n\n    /**\n     * Resolved key type if not a basic type.\n     * @type {ReflectionObject|null}\n     */\n    this.resolvedKeyType = null;\n\n    // Overrides Field#map\n    this.map = true;\n}\n\n/**\n * Map field descriptor.\n * @interface IMapField\n * @extends {IField}\n * @property {string} keyType Key type\n */\n\n/**\n * Extension map field descriptor.\n * @interface IExtensionMapField\n * @extends IMapField\n * @property {string} extend Extended type\n */\n\n/**\n * Constructs a map field from a map field descriptor.\n * @param {string} name Field name\n * @param {IMapField} json Map field descriptor\n * @returns {MapField} Created map field\n * @throws {TypeError} If arguments are invalid\n */\nMapField.fromJSON = function fromJSON(name, json) {\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\n};\n\n/**\n * Converts this map field to a map field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMapField} Map field descriptor\n */\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"keyType\" , this.keyType,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMapField.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\n    if (types.mapKey[this.keyType] === undefined)\n        throw Error(\"invalid key type: \" + this.keyType);\n\n    return Field.prototype.resolve.call(this);\n};\n\n/**\n * Map field decorator (TypeScript).\n * @name MapField.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\n * @returns {FieldDecorator} Decorator function\n * @template T extends { [key: string]: number | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\n */\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\n\n    // submessage value: decorate the submessage and use its name as the type\n    if (typeof fieldValueType === \"function\")\n        fieldValueType = util.decorateType(fieldValueType).name;\n\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\n    else if (fieldValueType && typeof fieldValueType === \"object\")\n        fieldValueType = util.decorateEnum(fieldValueType).name;\n\n    return function mapFieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\n    };\n};\n", "\"use strict\";\nmodule.exports = Message;\n\nvar util = require(35);\n\n/**\n * Constructs a new message instance.\n * @classdesc Abstract runtime message.\n * @constructor\n * @param {Properties<T>} [properties] Properties to set\n * @template T extends object = object\n */\nfunction Message(properties) {\n    // not used internally\n    if (properties)\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n            this[keys[i]] = properties[keys[i]];\n}\n\n/**\n * Reference to the reflected type.\n * @name Message.$type\n * @type {Type}\n * @readonly\n */\n\n/**\n * Reference to the reflected type.\n * @name Message#$type\n * @type {Type}\n * @readonly\n */\n\n/*eslint-disable valid-jsdoc*/\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<T>} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.create = function create(properties) {\n    return this.$type.create(properties);\n};\n\n/**\n * Encodes a message of this type.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encode = function encode(message, writer) {\n    return this.$type.encode(message, writer);\n};\n\n/**\n * Encodes a message of this type preceeded by its length as a varint.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.$type.encodeDelimited(message, writer);\n};\n\n/**\n * Decodes a message of this type.\n * @name Message.decode\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decode = function decode(reader) {\n    return this.$type.decode(reader);\n};\n\n/**\n * Decodes a message of this type preceeded by its length as a varint.\n * @name Message.decodeDelimited\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decodeDelimited = function decodeDelimited(reader) {\n    return this.$type.decodeDelimited(reader);\n};\n\n/**\n * Verifies a message of this type.\n * @name Message.verify\n * @function\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\n */\nMessage.verify = function verify(message) {\n    return this.$type.verify(message);\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object\n * @returns {T} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.fromObject = function fromObject(object) {\n    return this.$type.fromObject(object);\n};\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {T} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.toObject = function toObject(message, options) {\n    return this.$type.toObject(message, options);\n};\n\n/**\n * Converts this message to JSON.\n * @returns {Object.<string,*>} JSON object\n */\nMessage.prototype.toJSON = function toJSON() {\n    return this.$type.toObject(this, util.toJSONOptions);\n};\n\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\nmodule.exports = Method;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\n\nvar util = require(33);\n\n/**\n * Constructs a new service method instance.\n * @classdesc Reflected service method.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Method name\n * @param {string|undefined} type Method type, usually `\"rpc\"`\n * @param {string} requestType Request message type\n * @param {string} responseType Response message type\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this method\n */\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment) {\n\n    /* istanbul ignore next */\n    if (util.isObject(requestStream)) {\n        options = requestStream;\n        requestStream = responseStream = undefined;\n    } else if (util.isObject(responseStream)) {\n        options = responseStream;\n        responseStream = undefined;\n    }\n\n    /* istanbul ignore if */\n    if (!(type === undefined || util.isString(type)))\n        throw TypeError(\"type must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(requestType))\n        throw TypeError(\"requestType must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(responseType))\n        throw TypeError(\"responseType must be a string\");\n\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Method type.\n     * @type {string}\n     */\n    this.type = type || \"rpc\"; // toJSON\n\n    /**\n     * Request type.\n     * @type {string}\n     */\n    this.requestType = requestType; // toJSON, marker\n\n    /**\n     * Whether requests are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.requestStream = requestStream ? true : undefined; // toJSON\n\n    /**\n     * Response type.\n     * @type {string}\n     */\n    this.responseType = responseType; // toJSON\n\n    /**\n     * Whether responses are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.responseStream = responseStream ? true : undefined; // toJSON\n\n    /**\n     * Resolved request type.\n     * @type {Type|null}\n     */\n    this.resolvedRequestType = null;\n\n    /**\n     * Resolved response type.\n     * @type {Type|null}\n     */\n    this.resolvedResponseType = null;\n\n    /**\n     * Comment for this method\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Method descriptor.\n * @interface IMethod\n * @property {string} [type=\"rpc\"] Method type\n * @property {string} requestType Request type\n * @property {string} responseType Response type\n * @property {boolean} [requestStream=false] Whether requests are streamed\n * @property {boolean} [responseStream=false] Whether responses are streamed\n * @property {Object.<string,*>} [options] Method options\n */\n\n/**\n * Constructs a method from a method descriptor.\n * @param {string} name Method name\n * @param {IMethod} json Method descriptor\n * @returns {Method} Created method\n * @throws {TypeError} If arguments are invalid\n */\nMethod.fromJSON = function fromJSON(name, json) {\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment);\n};\n\n/**\n * Converts this method to a method descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMethod} Method descriptor\n */\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\n        \"requestType\"    , this.requestType,\n        \"requestStream\"  , this.requestStream,\n        \"responseType\"   , this.responseType,\n        \"responseStream\" , this.responseStream,\n        \"options\"        , this.options,\n        \"comment\"        , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMethod.prototype.resolve = function resolve() {\n\n    /* istanbul ignore if */\n    if (this.resolved)\n        return this;\n\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n", "\"use strict\";\nmodule.exports = Namespace;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\n\nvar Field    = require(15),\n    util     = require(33);\n\nvar Type,    // cyclic\n    Service,\n    Enum;\n\n/**\n * Constructs a new namespace instance.\n * @name Namespace\n * @classdesc Reflected namespace.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a namespace from JSON.\n * @memberof Namespace\n * @function\n * @param {string} name Namespace name\n * @param {Object.<string,*>} json JSON object\n * @returns {Namespace} Created namespace\n * @throws {TypeError} If arguments are invalid\n */\nNamespace.fromJSON = function fromJSON(name, json) {\n    return new Namespace(name, json.options).addJSON(json.nested);\n};\n\n/**\n * Converts an array of reflection objects to JSON.\n * @memberof Namespace\n * @param {ReflectionObject[]} array Object array\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\n */\nfunction arrayToJSON(array, toJSONOptions) {\n    if (!(array && array.length))\n        return undefined;\n    var obj = {};\n    for (var i = 0; i < array.length; ++i)\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\n    return obj;\n}\n\nNamespace.arrayToJSON = arrayToJSON;\n\n/**\n * Tests if the specified id is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedId = function isReservedId(reserved, id) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] > id)\n                return true;\n    return false;\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedName = function isReservedName(reserved, name) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (reserved[i] === name)\n                return true;\n    return false;\n};\n\n/**\n * Not an actual constructor. Use {@link Namespace} instead.\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports NamespaceBase\n * @extends ReflectionObject\n * @abstract\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n * @see {@link Namespace}\n */\nfunction Namespace(name, options) {\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Nested objects by name.\n     * @type {Object.<string,ReflectionObject>|undefined}\n     */\n    this.nested = undefined; // toJSON\n\n    /**\n     * Cached nested objects as an array.\n     * @type {ReflectionObject[]|null}\n     * @private\n     */\n    this._nestedArray = null;\n}\n\nfunction clearCache(namespace) {\n    namespace._nestedArray = null;\n    return namespace;\n}\n\n/**\n * Nested objects of this namespace as an array for iteration.\n * @name NamespaceBase#nestedArray\n * @type {ReflectionObject[]}\n * @readonly\n */\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\n    get: function() {\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\n    }\n});\n\n/**\n * Namespace descriptor.\n * @interface INamespace\n * @property {Object.<string,*>} [options] Namespace options\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\n */\n\n/**\n * Any extension field descriptor.\n * @typedef AnyExtensionField\n * @type {IExtensionField|IExtensionMapField}\n */\n\n/**\n * Any nested object descriptor.\n * @typedef AnyNestedObject\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace}\n */\n// ^ BEWARE: VSCode hangs forever when using more than 5 types (that's why AnyExtensionField exists in the first place)\n\n/**\n * Converts this namespace to a namespace descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {INamespace} Namespace descriptor\n */\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\n    return util.toObject([\n        \"options\" , this.options,\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\n    ]);\n};\n\n/**\n * Adds nested objects to this namespace from nested object descriptors.\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\n * @returns {Namespace} `this`\n */\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\n    var ns = this;\n    /* istanbul ignore else */\n    if (nestedJson) {\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\n            nested = nestedJson[names[i]];\n            ns.add( // most to least likely\n                ( nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : nested.id !== undefined\n                ? Field.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    }\n    return this;\n};\n\n/**\n * Gets the nested object of the specified name.\n * @param {string} name Nested object name\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\n */\nNamespace.prototype.get = function get(name) {\n    return this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Gets the values of the nested {@link Enum|enum} of the specified name.\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\n * @param {string} name Nested enum name\n * @returns {Object.<string,number>} Enum values\n * @throws {Error} If there is no such enum\n */\nNamespace.prototype.getEnum = function getEnum(name) {\n    if (this.nested && this.nested[name] instanceof Enum)\n        return this.nested[name].values;\n    throw Error(\"no such enum: \" + name);\n};\n\n/**\n * Adds a nested object to this namespace.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name\n */\nNamespace.prototype.add = function add(object) {\n\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type || object instanceof Enum || object instanceof Service || object instanceof Namespace))\n        throw TypeError(\"object must be a valid nested object\");\n\n    if (!this.nested)\n        this.nested = {};\n    else {\n        var prev = this.get(object.name);\n        if (prev) {\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\n                // replace plain namespace but keep existing nested elements and options\n                var nested = prev.nestedArray;\n                for (var i = 0; i < nested.length; ++i)\n                    object.add(nested[i]);\n                this.remove(prev);\n                if (!this.nested)\n                    this.nested = {};\n                object.setOptions(prev.options, true);\n\n            } else\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n        }\n    }\n    this.nested[object.name] = object;\n    object.onAdd(this);\n    return clearCache(this);\n};\n\n/**\n * Removes a nested object from this namespace.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this namespace\n */\nNamespace.prototype.remove = function remove(object) {\n\n    if (!(object instanceof ReflectionObject))\n        throw TypeError(\"object must be a ReflectionObject\");\n    if (object.parent !== this)\n        throw Error(object + \" is not a member of \" + this);\n\n    delete this.nested[object.name];\n    if (!Object.keys(this.nested).length)\n        this.nested = undefined;\n\n    object.onRemove(this);\n    return clearCache(this);\n};\n\n/**\n * Defines additial namespaces within this one if not yet existing.\n * @param {string|string[]} path Path to create\n * @param {*} [json] Nested types to create from JSON\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\n */\nNamespace.prototype.define = function define(path, json) {\n\n    if (util.isString(path))\n        path = path.split(\".\");\n    else if (!Array.isArray(path))\n        throw TypeError(\"illegal path\");\n    if (path && path.length && path[0] === \"\")\n        throw Error(\"path must be relative\");\n\n    var ptr = this;\n    while (path.length > 0) {\n        var part = path.shift();\n        if (ptr.nested && ptr.nested[part]) {\n            ptr = ptr.nested[part];\n            if (!(ptr instanceof Namespace))\n                throw Error(\"path conflicts with non-namespace objects\");\n        } else\n            ptr.add(ptr = new Namespace(part));\n    }\n    if (json)\n        ptr.addJSON(json);\n    return ptr;\n};\n\n/**\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\n * @returns {Namespace} `this`\n */\nNamespace.prototype.resolveAll = function resolveAll() {\n    var nested = this.nestedArray, i = 0;\n    while (i < nested.length)\n        if (nested[i] instanceof Namespace)\n            nested[i++].resolveAll();\n        else\n            nested[i++].resolve();\n    return this.resolve();\n};\n\n/**\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\n * @param {string|string[]} path Path to look up\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n */\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\n\n    /* istanbul ignore next */\n    if (typeof filterTypes === \"boolean\") {\n        parentAlreadyChecked = filterTypes;\n        filterTypes = undefined;\n    } else if (filterTypes && !Array.isArray(filterTypes))\n        filterTypes = [ filterTypes ];\n\n    if (util.isString(path) && path.length) {\n        if (path === \".\")\n            return this.root;\n        path = path.split(\".\");\n    } else if (!path.length)\n        return this;\n\n    // Start at root if path is absolute\n    if (path[0] === \"\")\n        return this.root.lookup(path.slice(1), filterTypes);\n\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\n    var found = this.get(path[0]);\n    if (found) {\n        if (path.length === 1) {\n            if (!filterTypes || filterTypes.indexOf(found.constructor) > -1)\n                return found;\n        } else if (found instanceof Namespace && (found = found.lookup(path.slice(1), filterTypes, true)))\n            return found;\n\n    // Otherwise try each nested namespace\n    } else\n        for (var i = 0; i < this.nestedArray.length; ++i)\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i].lookup(path, filterTypes, true)))\n                return found;\n\n    // If there hasn't been a match, try again at the parent\n    if (this.parent === null || parentAlreadyChecked)\n        return null;\n    return this.parent.lookup(path, filterTypes);\n};\n\n/**\n * Looks up the reflection object at the specified path, relative to this namespace.\n * @name NamespaceBase#lookup\n * @function\n * @param {string|string[]} path Path to look up\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @variation 2\n */\n// lookup(path: string, [parentAlreadyChecked: boolean])\n\n/**\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type\n * @throws {Error} If `path` does not point to a type\n */\nNamespace.prototype.lookupType = function lookupType(path) {\n    var found = this.lookup(path, [ Type ]);\n    if (!found)\n        throw Error(\"no such type: \" + path);\n    return found;\n};\n\n/**\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Enum} Looked up enum\n * @throws {Error} If `path` does not point to an enum\n */\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\n    var found = this.lookup(path, [ Enum ]);\n    if (!found)\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type or enum\n * @throws {Error} If `path` does not point to a type or enum\n */\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\n    var found = this.lookup(path, [ Type, Enum ]);\n    if (!found)\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Service} Looked up service\n * @throws {Error} If `path` does not point to a service\n */\nNamespace.prototype.lookupService = function lookupService(path) {\n    var found = this.lookup(path, [ Service ]);\n    if (!found)\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\n    return found;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nNamespace._configure = function(Type_, Service_, Enum_) {\n    Type    = Type_;\n    Service = Service_;\n    Enum    = Enum_;\n};\n", "\"use strict\";\nmodule.exports = ReflectionObject;\n\nReflectionObject.className = \"ReflectionObject\";\n\nvar util = require(33);\n\nvar Root; // cyclic\n\n/**\n * Constructs a new reflection object instance.\n * @classdesc Base class of all reflection objects.\n * @constructor\n * @param {string} name Object name\n * @param {Object.<string,*>} [options] Declared options\n * @abstract\n */\nfunction ReflectionObject(name, options) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (options && !util.isObject(options))\n        throw TypeError(\"options must be an object\");\n\n    /**\n     * Options.\n     * @type {Object.<string,*>|undefined}\n     */\n    this.options = options; // toJSON\n\n    /**\n     * Unique name within its namespace.\n     * @type {string}\n     */\n    this.name = name;\n\n    /**\n     * Parent namespace.\n     * @type {Namespace|null}\n     */\n    this.parent = null;\n\n    /**\n     * Whether already resolved or not.\n     * @type {boolean}\n     */\n    this.resolved = false;\n\n    /**\n     * Comment text, if any.\n     * @type {string|null}\n     */\n    this.comment = null;\n\n    /**\n     * Defining file name.\n     * @type {string|null}\n     */\n    this.filename = null;\n}\n\nObject.defineProperties(ReflectionObject.prototype, {\n\n    /**\n     * Reference to the root namespace.\n     * @name ReflectionObject#root\n     * @type {Root}\n     * @readonly\n     */\n    root: {\n        get: function() {\n            var ptr = this;\n            while (ptr.parent !== null)\n                ptr = ptr.parent;\n            return ptr;\n        }\n    },\n\n    /**\n     * Full name including leading dot.\n     * @name ReflectionObject#fullName\n     * @type {string}\n     * @readonly\n     */\n    fullName: {\n        get: function() {\n            var path = [ this.name ],\n                ptr = this.parent;\n            while (ptr) {\n                path.unshift(ptr.name);\n                ptr = ptr.parent;\n            }\n            return path.join(\".\");\n        }\n    }\n});\n\n/**\n * Converts this reflection object to its descriptor representation.\n * @returns {Object.<string,*>} Descriptor\n * @abstract\n */\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\n    throw Error(); // not implemented, shouldn't happen\n};\n\n/**\n * Called when this object is added to a parent.\n * @param {ReflectionObject} parent Parent added to\n * @returns {undefined}\n */\nReflectionObject.prototype.onAdd = function onAdd(parent) {\n    if (this.parent && this.parent !== parent)\n        this.parent.remove(this);\n    this.parent = parent;\n    this.resolved = false;\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleAdd(this);\n};\n\n/**\n * Called when this object is removed from a parent.\n * @param {ReflectionObject} parent Parent removed from\n * @returns {undefined}\n */\nReflectionObject.prototype.onRemove = function onRemove(parent) {\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleRemove(this);\n    this.parent = null;\n    this.resolved = false;\n};\n\n/**\n * Resolves this objects type references.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n    if (this.root instanceof Root)\n        this.resolved = true; // only if part of a root\n    return this;\n};\n\n/**\n * Gets an option value.\n * @param {string} name Option name\n * @returns {*} Option value or `undefined` if not set\n */\nReflectionObject.prototype.getOption = function getOption(name) {\n    if (this.options)\n        return this.options[name];\n    return undefined;\n};\n\n/**\n * Sets an option.\n * @param {string} name Option name\n * @param {*} value Option value\n * @param {boolean} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (!ifNotSet || !this.options || this.options[name] === undefined)\n        (this.options || (this.options = {}))[name] = value;\n    return this;\n};\n\n/**\n * Sets multiple options.\n * @param {Object.<string,*>} options Options to set\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\n    if (options)\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\n    return this;\n};\n\n/**\n * Converts this instance to its string representation.\n * @returns {string} Class name[, space, full name]\n */\nReflectionObject.prototype.toString = function toString() {\n    var className = this.constructor.className,\n        fullName  = this.fullName;\n    if (fullName.length)\n        return className + \" \" + fullName;\n    return className;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nReflectionObject._configure = function(Root_) {\n    Root = Root_;\n};\n", "\"use strict\";\nmodule.exports = OneOf;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\n\nvar Field = require(15),\n    util  = require(33);\n\n/**\n * Constructs a new oneof instance.\n * @classdesc Reflected oneof.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Oneof name\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction OneOf(name, fieldNames, options, comment) {\n    if (!Array.isArray(fieldNames)) {\n        options = fieldNames;\n        fieldNames = undefined;\n    }\n    ReflectionObject.call(this, name, options);\n\n    /* istanbul ignore if */\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\n        throw TypeError(\"fieldNames must be an Array\");\n\n    /**\n     * Field names that belong to this oneof.\n     * @type {string[]}\n     */\n    this.oneof = fieldNames || []; // toJSON, marker\n\n    /**\n     * Fields that belong to this oneof as an array for iteration.\n     * @type {Field[]}\n     * @readonly\n     */\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Oneof descriptor.\n * @interface IOneOf\n * @property {Array.<string>} oneof Oneof field names\n * @property {Object.<string,*>} [options] Oneof options\n */\n\n/**\n * Constructs a oneof from a oneof descriptor.\n * @param {string} name Oneof name\n * @param {IOneOf} json Oneof descriptor\n * @returns {OneOf} Created oneof\n * @throws {TypeError} If arguments are invalid\n */\nOneOf.fromJSON = function fromJSON(name, json) {\n    return new OneOf(name, json.oneof, json.options, json.comment);\n};\n\n/**\n * Converts this oneof to a oneof descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IOneOf} Oneof descriptor\n */\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , this.options,\n        \"oneof\"   , this.oneof,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Adds the fields of the specified oneof to the parent if not already done so.\n * @param {OneOf} oneof The oneof\n * @returns {undefined}\n * @inner\n * @ignore\n */\nfunction addFieldsToParent(oneof) {\n    if (oneof.parent)\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\n            if (!oneof.fieldsArray[i].parent)\n                oneof.parent.add(oneof.fieldsArray[i]);\n}\n\n/**\n * Adds a field to this oneof and removes it from its current parent, if any.\n * @param {Field} field Field to add\n * @returns {OneOf} `this`\n */\nOneOf.prototype.add = function add(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    if (field.parent && field.parent !== this.parent)\n        field.parent.remove(field);\n    this.oneof.push(field.name);\n    this.fieldsArray.push(field);\n    field.partOf = this; // field.parent remains null\n    addFieldsToParent(this);\n    return this;\n};\n\n/**\n * Removes a field from this oneof and puts it back to the oneof's parent.\n * @param {Field} field Field to remove\n * @returns {OneOf} `this`\n */\nOneOf.prototype.remove = function remove(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    var index = this.fieldsArray.indexOf(field);\n\n    /* istanbul ignore if */\n    if (index < 0)\n        throw Error(field + \" is not a member of \" + this);\n\n    this.fieldsArray.splice(index, 1);\n    index = this.oneof.indexOf(field.name);\n\n    /* istanbul ignore else */\n    if (index > -1) // theoretical\n        this.oneof.splice(index, 1);\n\n    field.partOf = null;\n    return this;\n};\n\n/**\n * @override\n */\nOneOf.prototype.onAdd = function onAdd(parent) {\n    ReflectionObject.prototype.onAdd.call(this, parent);\n    var self = this;\n    // Collect present fields\n    for (var i = 0; i < this.oneof.length; ++i) {\n        var field = parent.get(this.oneof[i]);\n        if (field && !field.partOf) {\n            field.partOf = self;\n            self.fieldsArray.push(field);\n        }\n    }\n    // Add not yet present fields\n    addFieldsToParent(this);\n};\n\n/**\n * @override\n */\nOneOf.prototype.onRemove = function onRemove(parent) {\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\n        if ((field = this.fieldsArray[i]).parent)\n            field.parent.remove(field);\n    ReflectionObject.prototype.onRemove.call(this, parent);\n};\n\n/**\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\n * @typedef OneOfDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} oneofName OneOf name\n * @returns {undefined}\n */\n\n/**\n * OneOf decorator (TypeScript).\n * @function\n * @param {...string} fieldNames Field names\n * @returns {OneOfDecorator} Decorator function\n * @template T extends string\n */\nOneOf.d = function decorateOneOf() {\n    var fieldNames = new Array(arguments.length),\n        index = 0;\n    while (index < arguments.length)\n        fieldNames[index] = arguments[index++];\n    return function oneOfDecorator(prototype, oneofName) {\n        util.decorateType(prototype.constructor)\n            .add(new OneOf(oneofName, fieldNames));\n        Object.defineProperty(prototype, oneofName, {\n            get: util.oneOfGetter(fieldNames),\n            set: util.oneOfSetter(fieldNames)\n        });\n    };\n};\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(35);\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = util.Buffer\n    ? function create_buffer_setup(buffer) {\n        return (Reader.create = function create_buffer(buffer) {\n            return util.Buffer.isBuffer(buffer)\n                ? new BufferReader(buffer)\n                /* istanbul ignore next */\n                : create_array(buffer);\n        })(buffer);\n    }\n    /* istanbul ignore next */\n    : create_array;\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/*\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/*\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/*\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/*\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/*\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n    return start === end // fix for IE 10/Win8 and others' subarray returning array of size 1\n        ? new this.buf.constructor(0)\n        : this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erReader;\n\n// extends Reader\nvar Reader = require(24);\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(35);\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\n/* istanbul ignore else */\nif (util.Buffer)\n    BufferReader.prototype._slice = util.Buffer.prototype.slice;\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {<PERSON><PERSON>er} Value read\n */\n", "\"use strict\";\nmodule.exports = Root;\n\n// extends Namespace\nvar Namespace = require(21);\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\n\nvar Field   = require(15),\n    Enum    = require(14),\n    OneOf   = require(23),\n    util    = require(33);\n\nvar Type,   // cyclic\n    parse,  // might be excluded\n    common; // \"\n\n/**\n * Constructs a new root namespace instance.\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\n * @extends NamespaceBase\n * @constructor\n * @param {Object.<string,*>} [options] Top level options\n */\nfunction Root(options) {\n    Namespace.call(this, \"\", options);\n\n    /**\n     * Deferred extension fields.\n     * @type {Field[]}\n     */\n    this.deferred = [];\n\n    /**\n     * Resolved file names of loaded files.\n     * @type {string[]}\n     */\n    this.files = [];\n}\n\n/**\n * Loads a namespace descriptor into a root namespace.\n * @param {INamespace} json Nameespace descriptor\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\n * @returns {Root} Root namespace\n */\nRoot.fromJSON = function fromJSON(json, root) {\n    if (!root)\n        root = new Root();\n    if (json.options)\n        root.setOptions(json.options);\n    return root.addJSON(json.nested);\n};\n\n/**\n * Resolves the path of an imported file, relative to the importing origin.\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\n * @function\n * @param {string} origin The file name of the importing file\n * @param {string} target The file name being imported\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\n */\nRoot.prototype.resolvePath = util.path.resolve;\n\n// A symbol-like function to safely signal synchronous loading\n/* istanbul ignore next */\nfunction SYNC() {} // eslint-disable-line no-empty-function\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} options Parse options\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.load = function load(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = undefined;\n    }\n    var self = this;\n    if (!callback)\n        return util.asPromise(load, self, filename, options);\n\n    var sync = callback === SYNC; // undocumented\n\n    // Finishes loading by calling the callback (exactly once)\n    function finish(err, root) {\n        /* istanbul ignore if */\n        if (!callback)\n            return;\n        var cb = callback;\n        callback = null;\n        if (sync)\n            throw err;\n        cb(err, root);\n    }\n\t\n    // Bundled definition existence checking\n    function getBundledFileName(filename) {\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\n        if (idx > -1) {\n            var altname = filename.substring(idx);\n            if (altname in common) return altname; \n        }\n        return null;\n    }\n\n    // Processes a single file\n    function process(filename, source) {\n        try {\n            if (util.isString(source) && source.charAt(0) === \"{\")\n                source = JSON.parse(source);\n            if (!util.isString(source))\n                self.setOptions(source.options).addJSON(source.nested);\n            else {\n                parse.filename = filename;\n                var parsed = parse(source, self, options),\n                    resolved,\n                    i = 0;\n                if (parsed.imports)\n                    for (; i < parsed.imports.length; ++i)\n                        if (resolved = (getBundledFileName(parsed.imports[i]) || self.resolvePath(filename, parsed.imports[i])))\n                            fetch(resolved);\n                if (parsed.weakImports)\n                    for (i = 0; i < parsed.weakImports.length; ++i)\n                        if (resolved = (getBundledFileName(parsed.weakImports[i]) || self.resolvePath(filename, parsed.weakImports[i])))\n                            fetch(resolved, true);\n            }\n        } catch (err) {\n            finish(err);\n        }\n        if (!sync && !queued)\n            finish(null, self); // only once anyway\n    }\n\n    // Fetches a single file\n    function fetch(filename, weak) {\n\n        // Skip if already loaded / attempted\n        if (self.files.indexOf(filename) > -1)\n            return;\n        self.files.push(filename);\n\n        // Shortcut bundled definitions\n        if (filename in common) {\n            if (sync)\n                process(filename, common[filename]);\n            else {\n                ++queued;\n                setTimeout(function() {\n                    --queued;\n                    process(filename, common[filename]);\n                });\n            }\n            return;\n        }\n\n        // Otherwise fetch from disk or network\n        if (sync) {\n            var source;\n            try {\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\n            } catch (err) {\n                if (!weak)\n                    finish(err);\n                return;\n            }\n            process(filename, source);\n        } else {\n            ++queued;\n            util.fetch(filename, function(err, source) {\n                --queued;\n                /* istanbul ignore if */\n                if (!callback)\n                    return; // terminated meanwhile\n                if (err) {\n                    /* istanbul ignore else */\n                    if (!weak)\n                        finish(err);\n                    else if (!queued) // can't be covered reliably\n                        finish(null, self);\n                    return;\n                }\n                process(filename, source);\n            });\n        }\n    }\n    var queued = 0;\n\n    // Assembling the root namespace doesn't require working type\n    // references anymore, so we can load everything in parallel\n    if (util.isString(filename))\n        filename = [ filename ];\n    for (var i = 0, resolved; i < filename.length; ++i)\n        if (resolved = self.resolvePath(\"\", filename[i]))\n            fetch(resolved);\n\n    if (sync)\n        return self;\n    if (!queued)\n        finish(null, self);\n    return undefined;\n};\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Promise<Root>} Promise\n * @variation 3\n */\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\n * @function Root#loadSync\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n */\nRoot.prototype.loadSync = function loadSync(filename, options) {\n    if (!util.isNode)\n        throw Error(\"not supported\");\n    return this.load(filename, options, SYNC);\n};\n\n/**\n * @override\n */\nRoot.prototype.resolveAll = function resolveAll() {\n    if (this.deferred.length)\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\n        }).join(\", \"));\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n// only uppercased (and thus conflict-free) children are exposed, see below\nvar exposeRe = /^[A-Z]/;\n\n/**\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\n * @param {Root} root Root instance\n * @param {Field} field Declaring extension field witin the declaring type\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\n * @inner\n * @ignore\n */\nfunction tryHandleExtension(root, field) {\n    var extendedType = field.parent.lookup(field.extend);\n    if (extendedType) {\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\n        sisterField.declaringField = field;\n        field.extensionField = sisterField;\n        extendedType.add(sisterField);\n        return true;\n    }\n    return false;\n}\n\n/**\n * Called when any object is added to this root or its sub-namespaces.\n * @param {ReflectionObject} object Object added\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleAdd = function _handleAdd(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\n            if (!tryHandleExtension(this, object))\n                this.deferred.push(object);\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\n\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\n\n        if (object instanceof Type) // Try to handle any deferred extensions\n            for (var i = 0; i < this.deferred.length;)\n                if (tryHandleExtension(this, this.deferred[i]))\n                    this.deferred.splice(i, 1);\n                else\n                    ++i;\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\n            this._handleAdd(object._nestedArray[j]);\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object; // expose namespace as property of its parent\n    }\n\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\n    // a static module with reflection-based solutions where the condition is met.\n};\n\n/**\n * Called when any object is removed from this root or its sub-namespaces.\n * @param {ReflectionObject} object Object removed\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleRemove = function _handleRemove(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field */ object.extend !== undefined) {\n            if (/* already handled */ object.extensionField) { // remove its sister field\n                object.extensionField.parent.remove(object.extensionField);\n                object.extensionField = null;\n            } else { // cancel the extension\n                var index = this.deferred.indexOf(object);\n                /* istanbul ignore else */\n                if (index > -1)\n                    this.deferred.splice(index, 1);\n            }\n        }\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose enum values\n\n    } else if (object instanceof Namespace) {\n\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\n            this._handleRemove(object._nestedArray[i]);\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose namespaces\n\n    }\n};\n\n// Sets up cyclic dependencies (called in index-light)\nRoot._configure = function(Type_, parse_, common_) {\n    Type   = Type_;\n    parse  = parse_;\n    common = common_;\n};\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available accross modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(29);\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(35);\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\nmodule.exports = Service;\n\n// extends Namespace\nvar Namespace = require(21);\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\n\nvar Method = require(20),\n    util   = require(33),\n    rpc    = require(28);\n\n/**\n * Constructs a new service instance.\n * @classdesc Reflected service.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Service name\n * @param {Object.<string,*>} [options] Service options\n * @throws {TypeError} If arguments are invalid\n */\nfunction Service(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Service methods.\n     * @type {Object.<string,Method>}\n     */\n    this.methods = {}; // toJSON, marker\n\n    /**\n     * Cached methods as an array.\n     * @type {Method[]|null}\n     * @private\n     */\n    this._methodsArray = null;\n}\n\n/**\n * Service descriptor.\n * @interface IService\n * @extends INamespace\n * @property {Object.<string,IMethod>} methods Method descriptors\n */\n\n/**\n * Constructs a service from a service descriptor.\n * @param {string} name Service name\n * @param {IService} json Service descriptor\n * @returns {Service} Created service\n * @throws {TypeError} If arguments are invalid\n */\nService.fromJSON = function fromJSON(name, json) {\n    var service = new Service(name, json.options);\n    /* istanbul ignore else */\n    if (json.methods)\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\n    if (json.nested)\n        service.addJSON(json.nested);\n    service.comment = json.comment;\n    return service;\n};\n\n/**\n * Converts this service to a service descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IService} Service descriptor\n */\nService.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , inherited && inherited.options || undefined,\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\n        \"nested\"  , inherited && inherited.nested || undefined,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Methods of this service as an array for iteration.\n * @name Service#methodsArray\n * @type {Method[]}\n * @readonly\n */\nObject.defineProperty(Service.prototype, \"methodsArray\", {\n    get: function() {\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\n    }\n});\n\nfunction clearCache(service) {\n    service._methodsArray = null;\n    return service;\n}\n\n/**\n * @override\n */\nService.prototype.get = function get(name) {\n    return this.methods[name]\n        || Namespace.prototype.get.call(this, name);\n};\n\n/**\n * @override\n */\nService.prototype.resolveAll = function resolveAll() {\n    var methods = this.methodsArray;\n    for (var i = 0; i < methods.length; ++i)\n        methods[i].resolve();\n    return Namespace.prototype.resolve.call(this);\n};\n\n/**\n * @override\n */\nService.prototype.add = function add(object) {\n\n    /* istanbul ignore if */\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Method) {\n        this.methods[object.name] = object;\n        object.parent = this;\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * @override\n */\nService.prototype.remove = function remove(object) {\n    if (object instanceof Method) {\n\n        /* istanbul ignore if */\n        if (this.methods[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.methods[object.name];\n        object.parent = null;\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Creates a runtime service using the specified rpc implementation.\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\n */\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\n            m: method,\n            q: method.resolvedRequestType.ctor,\n            s: method.resolvedResponseType.ctor\n        });\n    }\n    return rpcService;\n};\n", "\"use strict\";\nmodule.exports = Type;\n\n// extends Namespace\nvar Namespace = require(21);\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\n\nvar Enum      = require(14),\n    OneOf     = require(23),\n    Field     = require(15),\n    MapField  = require(18),\n    Service   = require(30),\n    Message   = require(19),\n    Reader    = require(24),\n    Writer    = require(38),\n    util      = require(33),\n    encoder   = require(13),\n    decoder   = require(12),\n    verifier  = require(36),\n    converter = require(11),\n    wrappers  = require(37);\n\n/**\n * Constructs a new reflected message type instance.\n * @classdesc Reflected message type.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Message name\n * @param {Object.<string,*>} [options] Declared options\n */\nfunction Type(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Message fields.\n     * @type {Object.<string,Field>}\n     */\n    this.fields = {};  // toJSON, marker\n\n    /**\n     * Oneofs declared within this namespace, if any.\n     * @type {Object.<string,OneOf>}\n     */\n    this.oneofs = undefined; // toJSON\n\n    /**\n     * Extension ranges, if any.\n     * @type {number[][]}\n     */\n    this.extensions = undefined; // toJSON\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    /*?\n     * Whether this type is a legacy group.\n     * @type {boolean|undefined}\n     */\n    this.group = undefined; // toJSON\n\n    /**\n     * Cached fields by id.\n     * @type {Object.<number,Field>|null}\n     * @private\n     */\n    this._fieldsById = null;\n\n    /**\n     * Cached fields as an array.\n     * @type {Field[]|null}\n     * @private\n     */\n    this._fieldsArray = null;\n\n    /**\n     * Cached oneofs as an array.\n     * @type {OneOf[]|null}\n     * @private\n     */\n    this._oneofsArray = null;\n\n    /**\n     * Cached constructor.\n     * @type {Constructor<{}>}\n     * @private\n     */\n    this._ctor = null;\n}\n\nObject.defineProperties(Type.prototype, {\n\n    /**\n     * Message fields by id.\n     * @name Type#fieldsById\n     * @type {Object.<number,Field>}\n     * @readonly\n     */\n    fieldsById: {\n        get: function() {\n\n            /* istanbul ignore if */\n            if (this._fieldsById)\n                return this._fieldsById;\n\n            this._fieldsById = {};\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\n                var field = this.fields[names[i]],\n                    id = field.id;\n\n                /* istanbul ignore if */\n                if (this._fieldsById[id])\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\n\n                this._fieldsById[id] = field;\n            }\n            return this._fieldsById;\n        }\n    },\n\n    /**\n     * Fields of this message as an array for iteration.\n     * @name Type#fieldsArray\n     * @type {Field[]}\n     * @readonly\n     */\n    fieldsArray: {\n        get: function() {\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\n        }\n    },\n\n    /**\n     * Oneofs of this message as an array for iteration.\n     * @name Type#oneofsArray\n     * @type {OneOf[]}\n     * @readonly\n     */\n    oneofsArray: {\n        get: function() {\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\n        }\n    },\n\n    /**\n     * The registered constructor, if any registered, otherwise a generic constructor.\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\n     * @name Type#ctor\n     * @type {Constructor<{}>}\n     */\n    ctor: {\n        get: function() {\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\n        },\n        set: function(ctor) {\n\n            // Ensure proper prototype\n            var prototype = ctor.prototype;\n            if (!(prototype instanceof Message)) {\n                (ctor.prototype = new Message()).constructor = ctor;\n                util.merge(ctor.prototype, prototype);\n            }\n\n            // Classes and messages reference their reflected type\n            ctor.$type = ctor.prototype.$type = this;\n\n            // Mix in static methods\n            util.merge(ctor, Message, true);\n\n            this._ctor = ctor;\n\n            // Messages have non-enumerable default values on their prototype\n            var i = 0;\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\n                this._fieldsArray[i].resolve(); // ensures a proper value\n\n            // Messages have non-enumerable getters and setters for each virtual oneof field\n            var ctorProperties = {};\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\n                };\n            if (i)\n                Object.defineProperties(ctor.prototype, ctorProperties);\n        }\n    }\n});\n\n/**\n * Generates a constructor function for the specified type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nType.generateConstructor = function generateConstructor(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"p\"], mtype.name);\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\n        if ((field = mtype._fieldsArray[i]).map) gen\n            (\"this%s={}\", util.safeProp(field.name));\n        else if (field.repeated) gen\n            (\"this%s=[]\", util.safeProp(field.name));\n    return gen\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\n        (\"this[ks[i]]=p[ks[i]]\");\n    /* eslint-enable no-unexpected-multiline */\n};\n\nfunction clearCache(type) {\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\n    delete type.encode;\n    delete type.decode;\n    delete type.verify;\n    return type;\n}\n\n/**\n * Message type descriptor.\n * @interface IType\n * @extends INamespace\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\n * @property {Object.<string,IField>} fields Field descriptors\n * @property {number[][]} [extensions] Extension ranges\n * @property {number[][]} [reserved] Reserved ranges\n * @property {boolean} [group=false] Whether a legacy group or not\n */\n\n/**\n * Creates a message type from a message type descriptor.\n * @param {string} name Message name\n * @param {IType} json Message type descriptor\n * @returns {Type} Created message type\n */\nType.fromJSON = function fromJSON(name, json) {\n    var type = new Type(name, json.options);\n    type.extensions = json.extensions;\n    type.reserved = json.reserved;\n    var names = Object.keys(json.fields),\n        i = 0;\n    for (; i < names.length; ++i)\n        type.add(\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\n            ? MapField.fromJSON\n            : Field.fromJSON )(names[i], json.fields[names[i]])\n        );\n    if (json.oneofs)\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\n    if (json.nested)\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\n            var nested = json.nested[names[i]];\n            type.add( // most to least likely\n                ( nested.id !== undefined\n                ? Field.fromJSON\n                : nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    if (json.extensions && json.extensions.length)\n        type.extensions = json.extensions;\n    if (json.reserved && json.reserved.length)\n        type.reserved = json.reserved;\n    if (json.group)\n        type.group = true;\n    if (json.comment)\n        type.comment = json.comment;\n    return type;\n};\n\n/**\n * Converts this message type to a message type descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IType} Message type descriptor\n */\nType.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"    , inherited && inherited.options || undefined,\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"group\"      , this.group || undefined,\n        \"nested\"     , inherited && inherited.nested || undefined,\n        \"comment\"    , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nType.prototype.resolveAll = function resolveAll() {\n    var fields = this.fieldsArray, i = 0;\n    while (i < fields.length)\n        fields[i++].resolve();\n    var oneofs = this.oneofsArray; i = 0;\n    while (i < oneofs.length)\n        oneofs[i++].resolve();\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n/**\n * @override\n */\nType.prototype.get = function get(name) {\n    return this.fields[name]\n        || this.oneofs && this.oneofs[name]\n        || this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Adds a nested object to this type.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\n */\nType.prototype.add = function add(object) {\n\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Field && object.extend === undefined) {\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\n        // The root object takes care of adding distinct sister-fields to the respective extended\n        // type instead.\n\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\n        if (this.isReservedId(object.id))\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\n        if (this.isReservedName(object.name))\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\n\n        if (object.parent)\n            object.parent.remove(object);\n        this.fields[object.name] = object;\n        object.message = this;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n        if (!this.oneofs)\n            this.oneofs = {};\n        this.oneofs[object.name] = object;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * Removes a nested object from this type.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this type\n */\nType.prototype.remove = function remove(object) {\n    if (object instanceof Field && object.extend === undefined) {\n        // See Type#add for the reason why extension fields are excluded here.\n\n        /* istanbul ignore if */\n        if (!this.fields || this.fields[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.fields[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n\n        /* istanbul ignore if */\n        if (!this.oneofs || this.oneofs[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.oneofs[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<{}>} Message instance\n */\nType.prototype.create = function create(properties) {\n    return new this.ctor(properties);\n};\n\n/**\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\n * @returns {Type} `this`\n */\nType.prototype.setup = function setup() {\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\n    // multiple times (V8, soft-deopt prototype-check).\n\n    var fullName = this.fullName,\n        types    = [];\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\n        types.push(this._fieldsArray[i].resolve().resolvedType);\n\n    // Replace setup methods with type-specific generated functions\n    this.encode = encoder(this)({\n        Writer : Writer,\n        types  : types,\n        util   : util\n    });\n    this.decode = decoder(this)({\n        Reader : Reader,\n        types  : types,\n        util   : util\n    });\n    this.verify = verifier(this)({\n        types : types,\n        util  : util\n    });\n    this.fromObject = converter.fromObject(this)({\n        types : types,\n        util  : util\n    });\n    this.toObject = converter.toObject(this)({\n        types : types,\n        util  : util\n    });\n\n    // Inject custom wrappers for common types\n    var wrapper = wrappers[fullName];\n    if (wrapper) {\n        var originalThis = Object.create(this);\n        // if (wrapper.fromObject) {\n            originalThis.fromObject = this.fromObject;\n            this.fromObject = wrapper.fromObject.bind(originalThis);\n        // }\n        // if (wrapper.toObject) {\n            originalThis.toObject = this.toObject;\n            this.toObject = wrapper.toObject.bind(originalThis);\n        // }\n    }\n\n    return this;\n};\n\n/**\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encode = function encode_setup(message, writer) {\n    return this.setup().encode(message, writer); // overrides this method\n};\n\n/**\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\n};\n\n/**\n * Decodes a message of this type.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @param {number} [length] Length of the message, if known beforehand\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError<{}>} If required fields are missing\n */\nType.prototype.decode = function decode_setup(reader, length) {\n    return this.setup().decode(reader, length); // overrides this method\n};\n\n/**\n * Decodes a message of this type preceeded by its byte length as a varint.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError} If required fields are missing\n */\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\n    if (!(reader instanceof Reader))\n        reader = Reader.create(reader);\n    return this.decode(reader, reader.uint32());\n};\n\n/**\n * Verifies that field values are valid and that required fields are present.\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\n */\nType.prototype.verify = function verify_setup(message) {\n    return this.setup().verify(message); // overrides this method\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object to convert\n * @returns {Message<{}>} Message instance\n */\nType.prototype.fromObject = function fromObject(object) {\n    return this.setup().fromObject(object);\n};\n\n/**\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\n * @interface IConversionOptions\n * @property {Function} [longs] Long conversion type.\n * Valid values are `String` and `Number` (the global types).\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\n * @property {Function} [enums] Enum value conversion type.\n * Only valid value is `String` (the global type).\n * Defaults to copy the present value, which is the numeric id.\n * @property {Function} [bytes] Bytes value conversion type.\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\n */\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n */\nType.prototype.toObject = function toObject(message, options) {\n    return this.setup().toObject(message, options);\n};\n\n/**\n * Decorator function as returned by {@link Type.d} (TypeScript).\n * @typedef TypeDecorator\n * @type {function}\n * @param {Constructor<T>} target Target constructor\n * @returns {undefined}\n * @template T extends Message<T>\n */\n\n/**\n * Type decorator (TypeScript).\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {TypeDecorator<T>} Decorator function\n * @template T extends Message<T>\n */\nType.d = function decorateType(typeName) {\n    return function typeDecorator(target) {\n        util.decorateType(target, typeName);\n    };\n};\n", "\"use strict\";\n\n/**\n * Common type constants.\n * @namespace\n */\nvar types = exports;\n\nvar util = require(33);\n\nvar s = [\n    \"double\",   // 0\n    \"float\",    // 1\n    \"int32\",    // 2\n    \"uint32\",   // 3\n    \"sint32\",   // 4\n    \"fixed32\",  // 5\n    \"sfixed32\", // 6\n    \"int64\",    // 7\n    \"uint64\",   // 8\n    \"sint64\",   // 9\n    \"fixed64\",  // 10\n    \"sfixed64\", // 11\n    \"bool\",     // 12\n    \"string\",   // 13\n    \"bytes\"     // 14\n];\n\nfunction bake(values, offset) {\n    var i = 0, o = {};\n    offset |= 0;\n    while (i < values.length) o[s[i + offset]] = values[i++];\n    return o;\n}\n\n/**\n * Basic type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n * @property {number} bytes=2 Ldelim wire type\n */\ntypes.basic = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2,\n    /* bytes    */ 2\n]);\n\n/**\n * Basic type defaults.\n * @type {Object.<string,*>}\n * @const\n * @property {number} double=0 Double default\n * @property {number} float=0 Float default\n * @property {number} int32=0 Int32 default\n * @property {number} uint32=0 Uint32 default\n * @property {number} sint32=0 Sint32 default\n * @property {number} fixed32=0 Fixed32 default\n * @property {number} sfixed32=0 Sfixed32 default\n * @property {number} int64=0 Int64 default\n * @property {number} uint64=0 Uint64 default\n * @property {number} sint64=0 Sint32 default\n * @property {number} fixed64=0 Fixed64 default\n * @property {number} sfixed64=0 Sfixed64 default\n * @property {boolean} bool=false Bool default\n * @property {string} string=\"\" String default\n * @property {Array.<number>} bytes=Array(0) Bytes default\n * @property {null} message=null Message default\n */\ntypes.defaults = bake([\n    /* double   */ 0,\n    /* float    */ 0,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 0,\n    /* sfixed32 */ 0,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 0,\n    /* sfixed64 */ 0,\n    /* bool     */ false,\n    /* string   */ \"\",\n    /* bytes    */ util.emptyArray,\n    /* message  */ null\n]);\n\n/**\n * Basic long type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n */\ntypes.long = bake([\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1\n], 7);\n\n/**\n * Allowed types for map keys with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n */\ntypes.mapKey = bake([\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2\n], 2);\n\n/**\n * Allowed types for packed repeated fields with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n */\ntypes.packed = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0\n]);\n", "\"use strict\";\n\n/**\n * Various utility functions.\n * @namespace\n */\nvar util = module.exports = require(35);\n\nvar roots = require(27);\n\nvar Type, // cyclic\n    Enum;\n\nutil.codegen = require(3);\nutil.fetch   = require(5);\nutil.path    = require(8);\n\n/**\n * Node's fs module if available.\n * @type {Object.<string,*>}\n */\nutil.fs = util.inquire(\"fs\");\n\n/**\n * Converts an object's values to an array.\n * @param {Object.<string,*>} object Object to convert\n * @returns {Array.<*>} Converted array\n */\nutil.toArray = function toArray(object) {\n    if (object) {\n        var keys  = Object.keys(object),\n            array = new Array(keys.length),\n            index = 0;\n        while (index < keys.length)\n            array[index] = object[keys[index++]];\n        return array;\n    }\n    return [];\n};\n\n/**\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\n * @param {Array.<*>} array Array to convert\n * @returns {Object.<string,*>} Converted object\n */\nutil.toObject = function toObject(array) {\n    var object = {},\n        index  = 0;\n    while (index < array.length) {\n        var key = array[index++],\n            val = array[index++];\n        if (val !== undefined)\n            object[key] = val;\n    }\n    return object;\n};\n\nvar safePropBackslashRe = /\\\\/g,\n    safePropQuoteRe     = /\"/g;\n\n/**\n * Tests whether the specified name is a reserved word in JS.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nutil.isReserved = function isReserved(name) {\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\n};\n\n/**\n * Returns a safe property accessor for the specified property name.\n * @param {string} prop Property name\n * @returns {string} Safe accessor\n */\nutil.safeProp = function safeProp(prop) {\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\n    return \".\" + prop;\n};\n\n/**\n * Converts the first character of a string to upper case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.ucFirst = function ucFirst(str) {\n    return str.charAt(0).toUpperCase() + str.substring(1);\n};\n\nvar camelCaseRe = /_([a-z])/g;\n\n/**\n * Converts a string to camel case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.camelCase = function camelCase(str) {\n    return str.substring(0, 1)\n         + str.substring(1)\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\n};\n\n/**\n * Compares reflected fields by id.\n * @param {Field} a First field\n * @param {Field} b Second field\n * @returns {number} Comparison value\n */\nutil.compareFieldsById = function compareFieldsById(a, b) {\n    return a.id - b.id;\n};\n\n/**\n * Decorator helper for types (TypeScript).\n * @param {Constructor<T>} ctor Constructor function\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {Type} Reflected type\n * @template T extends Message<T>\n * @property {Root} root Decorators root\n */\nutil.decorateType = function decorateType(ctor, typeName) {\n\n    /* istanbul ignore if */\n    if (ctor.$type) {\n        if (typeName && ctor.$type.name !== typeName) {\n            util.decorateRoot.remove(ctor.$type);\n            ctor.$type.name = typeName;\n            util.decorateRoot.add(ctor.$type);\n        }\n        return ctor.$type;\n    }\n\n    /* istanbul ignore next */\n    if (!Type)\n        Type = require(31);\n\n    var type = new Type(typeName || ctor.name);\n    util.decorateRoot.add(type);\n    type.ctor = ctor; // sets up .encode, .decode etc.\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\n    return type;\n};\n\nvar decorateEnumIndex = 0;\n\n/**\n * Decorator helper for enums (TypeScript).\n * @param {Object} object Enum object\n * @returns {Enum} Reflected enum\n */\nutil.decorateEnum = function decorateEnum(object) {\n\n    /* istanbul ignore if */\n    if (object.$type)\n        return object.$type;\n\n    /* istanbul ignore next */\n    if (!Enum)\n        Enum = require(14);\n\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\n    util.decorateRoot.add(enm);\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\n    return enm;\n};\n\n/**\n * Decorator root (TypeScript).\n * @name util.decorateRoot\n * @type {Root}\n * @readonly\n */\nObject.defineProperty(util, \"decorateRoot\", {\n    get: function() {\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(26))());\n    }\n});\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(35);\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/*\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(1);\n\n// converts to / from base64 encoded strings\nutil.base64 = require(2);\n\n// base class of rpc.Service\nutil.EventEmitter = require(4);\n\n// float handling accross browsers\nutil.float = require(6);\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(7);\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(10);\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(9);\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(34);\n\n// global object reference\nutil.global = typeof window !== \"undefined\" && window\n           || typeof global !== \"undefined\" && global\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n * @const\n */\nutil.isNode = Boolean(util.global.process && util.global.process.versions && util.global.process.versions.node);\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/*\n * Long.js's Long class if available and $ENABLE_LONG is set. This lets us leave it on\n * for this package's tests but have it be off in actual usage-reporting-protobuf use.\n * (We leave it on for some mode where there is no `process` that is used by tests.)\n */\nutil.Long = (typeof process === 'undefined' || process.env.ENABLE_LONG) ? (/* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\")) : undefined;\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/*\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/*\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: (new Error()).stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    (CustomError.prototype = Object.create(Error.prototype)).constructor = CustomError;\n\n    Object.defineProperty(CustomError.prototype, \"name\", { get: function() { return name; } });\n\n    CustomError.prototype.toString = function toString() {\n        return this.name + \": \" + this.message;\n    };\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = verifier;\n\nvar Enum      = require(14),\n    util      = require(33);\n\nfunction invalid(field, expected) {\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\n}\n\n/**\n * Generates a partial value verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref)\n                (\"default:\")\n                    (\"return%j\", invalid(field, \"enum value\"));\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\n            gen\n                    (\"break\")\n            (\"}\");\n        } else {\n            gen\n            (\"{\")\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\n                (\"if(e)\")\n                    (\"return%j+e\", field.name + \".\")\n            (\"}\");\n        }\n    } else {\n        switch (field.type) {\n            case \"int32\":\n            case \"uint32\":\n            case \"sint32\":\n            case \"fixed32\":\n            case \"sfixed32\": gen\n                (\"if(!util.isInteger(%s))\", ref)\n                    (\"return%j\", invalid(field, \"integer\"));\n                break;\n            case \"int64\":\n            case \"uint64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\n                    (\"return%j\", invalid(field, \"integer|Long\"));\n                break;\n            case \"float\":\n            case \"double\": gen\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\n                    (\"return%j\", invalid(field, \"number\"));\n                break;\n            case \"bool\": gen\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\n                    (\"return%j\", invalid(field, \"boolean\"));\n                break;\n            case \"string\": gen\n                (\"if(!util.isString(%s))\", ref)\n                    (\"return%j\", invalid(field, \"string\"));\n                break;\n            case \"bytes\": gen\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\n                    (\"return%j\", invalid(field, \"buffer\"));\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a partial key verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyKey(gen, field, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    switch (field.keyType) {\n        case \"int32\":\n        case \"uint32\":\n        case \"sint32\":\n        case \"fixed32\":\n        case \"sfixed32\": gen\n            (\"if(!util.key32Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"integer key\"));\n            break;\n        case \"int64\":\n        case \"uint64\":\n        case \"sint64\":\n        case \"fixed64\":\n        case \"sfixed64\": gen\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\n                (\"return%j\", invalid(field, \"integer|Long key\"));\n            break;\n        case \"bool\": gen\n            (\"if(!util.key2Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"boolean key\"));\n            break;\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a verifier specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction verifier(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\n        (\"return%j\", \"object expected\");\n    var oneofs = mtype.oneofsArray,\n        seenFirstField = {};\n    if (oneofs.length) gen\n    (\"var p={}\");\n\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            ref   = \"m\" + util.safeProp(field.name);\n\n        if (field.optional) gen\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\n\n        // map fields\n        if (field.map) { gen\n            (\"if(!util.isObject(%s))\", ref)\n                (\"return%j\", invalid(field, \"object\"))\n            (\"var k=Object.keys(%s)\", ref)\n            (\"for(var i=0;i<k.length;++i){\");\n                genVerifyKey(gen, field, \"k[i]\");\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\n            (\"}\");\n\n        // repeated fields\n        } else if (field.repeated) {\n          var arrayRef = ref;\n          if (field.useToArray()) {\n            arrayRef = \"array\" + field.id;\n            gen(\"var %s\", arrayRef);\n            gen(\"if (%s!=null&&%s.toArray) { %s = %s.toArray() } else { %s = %s }\",\n                ref, ref, arrayRef, ref, arrayRef, ref);\n          }\n          gen\n            (\"if(!Array.isArray(%s))\", arrayRef)\n                (\"return%j\", invalid(field, \"array\"))\n            (\"for(var i=0;i<%s.length;++i){\", arrayRef);\n                if (field.preEncoded()) {\n                  gen(\"if (!(%s instanceof Uint8Array)) {\", arrayRef + \"[i]\")\n                }\n                genVerifyValue(gen, field, i, arrayRef + \"[i]\")\n                if (field.preEncoded()) {\n                  gen(\"}\");\n                }\n            gen(\"}\");\n\n        // required or present fields\n        } else {\n            if (field.partOf) {\n                var oneofProp = util.safeProp(field.partOf.name);\n                if (seenFirstField[field.partOf.name] === 1) gen\n            (\"if(p%s===1)\", oneofProp)\n                (\"return%j\", field.partOf.name + \": multiple values\");\n                seenFirstField[field.partOf.name] = 1;\n                gen\n            (\"p%s=1\", oneofProp);\n            }\n            genVerifyValue(gen, field, i, ref);\n        }\n        if (field.optional) gen\n        (\"}\");\n    }\n    return gen\n    (\"return null\");\n    /* eslint-enable no-unexpected-multiline */\n}", "\"use strict\";\n\n/**\n * Wrappers for common types.\n * @type {Object.<string,IWrapper>}\n * @const\n */\nvar wrappers = exports;\n\nvar Message = require(19);\n\n/**\n * From object converter part of an {@link IWrapper}.\n * @typedef WrapperFromObjectConverter\n * @type {function}\n * @param {Object.<string,*>} object Plain object\n * @returns {Message<{}>} Message instance\n * @this Type\n */\n\n/**\n * To object converter part of an {@link IWrapper}.\n * @typedef WrapperToObjectConverter\n * @type {function}\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @this Type\n */\n\n/**\n * Common type wrapper part of {@link wrappers}.\n * @interface IWrapper\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\n * @property {WrapperToObjectConverter} [toObject] To object converter\n */\n\n// Custom wrapper for Any\nwrappers[\".google.protobuf.Any\"] = {\n\n    fromObject: function(object) {\n\n        // unwrap value type if mapped\n        if (object && object[\"@type\"]) {\n            var type = this.lookup(object[\"@type\"]);\n            /* istanbul ignore else */\n            if (type) {\n                // type_url does not accept leading \".\"\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\n                    object[\"@type\"].substr(1) : object[\"@type\"];\n                // type_url prefix is optional, but path seperator is required\n                return this.create({\n                    type_url: \"/\" + type_url,\n                    value: type.encode(type.fromObject(object)).finish()\n                });\n            }\n        }\n\n        return this.fromObject(object);\n    },\n\n    toObject: function(message, options) {\n\n        // decode value if requested and unmapped\n        if (options && options.json && message.type_url && message.value) {\n            // Only use fully qualified type name after the last '/'\n            var name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type)\n                message = type.decode(message.value);\n        }\n\n        // wrap value if unmapped\n        if (!(message instanceof this.ctor) && message instanceof Message) {\n            var object = message.$type.toObject(message, options);\n            object[\"@type\"] = message.$type.fullName;\n            return object;\n        }\n\n        return this.toObject(message, options);\n    }\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(35);\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = util.Buffer\n    ? function create_buffer_setup() {\n        return (Writer.create = function create_buffer() {\n            return new BufferWriter();\n        })();\n    }\n    /* istanbul ignore next */\n    : function create_array() {\n        return new Writer();\n    };\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(38);\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(35);\n\nvar Buffer = util.Buffer;\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Buffer} Buffer\n */\nBufferWriter.alloc = function alloc_buffer(size) {\n    return (BufferWriter.alloc = util._Buffer_allocUnsafe)(size);\n};\n\nvar writeBytesBuffer = Buffer && Buffer.prototype instanceof Uint8Array && Buffer.prototype.set.name === \"set\"\n    ? function writeBytesBuffer_set(val, buf, pos) {\n        buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\n                           // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytesBuffer_copy(val, buf, pos) {\n        if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n        else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n    };\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else\n        buf.utf8Write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n"], "sourceRoot": "."}