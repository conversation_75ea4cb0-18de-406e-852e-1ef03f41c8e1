const { sequelize } = require('../config/database');
const { DataTypes } = require('sequelize');

// 角色模型
const Role = sequelize.define('Role', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '角色名称：grower(种植者), processor(加工商), distributor(经销商), consumer(消费者)'
  },
  display_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '角色显示名称'
  },
  description: {
    type: DataTypes.TEXT,
    comment: '角色描述'
  }
}, {
  tableName: 'roles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 用户模型
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '用户名'
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    comment: '邮箱'
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '密码哈希'
  },
  role_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '角色ID'
  },
  real_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '真实姓名'
  },
  phone: {
    type: DataTypes.STRING(20),
    comment: '电话号码'
  },
  company_name: {
    type: DataTypes.STRING(200),
    comment: '公司名称'
  },
  address: {
    type: DataTypes.TEXT,
    comment: '地址'
  },
  license_number: {
    type: DataTypes.STRING(100),
    comment: '营业执照号码'
  },
  status: {
    type: DataTypes.ENUM('pending', 'active', 'suspended', 'rejected'),
    defaultValue: 'pending',
    comment: '用户状态'
  },
  is_online: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否在线'
  },
  last_login_at: {
    type: DataTypes.DATE,
    comment: '最后登录时间'
  },
  last_offline_at: {
    type: DataTypes.DATE,
    comment: '最后离线时间'
  },
  blockchain_address: {
    type: DataTypes.STRING(42),
    comment: '区块链地址'
  }
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 注册申请模型
const RegistrationApplication = sequelize.define('RegistrationApplication', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '申请用户名'
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '申请邮箱'
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '密码哈希'
  },
  role_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '申请角色ID'
  },
  real_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '真实姓名'
  },
  phone: {
    type: DataTypes.STRING(20),
    comment: '电话号码'
  },
  company_name: {
    type: DataTypes.STRING(200),
    comment: '公司名称'
  },
  address: {
    type: DataTypes.TEXT,
    comment: '地址'
  },
  license_number: {
    type: DataTypes.STRING(100),
    comment: '营业执照号码'
  },
  application_reason: {
    type: DataTypes.TEXT,
    comment: '申请理由'
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected'),
    defaultValue: 'pending',
    comment: '申请状态'
  },
  admin_comment: {
    type: DataTypes.TEXT,
    comment: '管理员备注'
  },
  applied_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '申请时间'
  },
  reviewed_at: {
    type: DataTypes.DATE,
    comment: '审核时间'
  },
  reviewed_by: {
    type: DataTypes.INTEGER,
    comment: '审核管理员ID'
  }
}, {
  tableName: 'registration_applications',
  timestamps: false
});

// 管理员模型
const Admin = sequelize.define('Admin', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '管理员用户名'
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '密码哈希'
  },
  real_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '真实姓名'
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    comment: '邮箱'
  },
  is_super_admin: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否为超级管理员'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否激活'
  },
  last_login_at: {
    type: DataTypes.DATE,
    comment: '最后登录时间'
  }
}, {
  tableName: 'admins',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 咖啡豆批次模型
const CoffeeBatch = sequelize.define('CoffeeBatch', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  batch_number: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '批次号'
  },
  grower_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '种植者ID'
  },
  variety: {
    type: DataTypes.STRING(100),
    comment: '咖啡品种'
  },
  origin_location: {
    type: DataTypes.STRING(200),
    comment: '种植地点'
  },
  planting_date: {
    type: DataTypes.DATEONLY,
    comment: '种植日期'
  },
  harvest_date: {
    type: DataTypes.DATEONLY,
    comment: '收获日期'
  },
  processing_method: {
    type: DataTypes.STRING(100),
    comment: '处理方法'
  },
  quality_grade: {
    type: DataTypes.STRING(50),
    comment: '质量等级'
  },
  quantity_kg: {
    type: DataTypes.DECIMAL(10, 2),
    comment: '数量(公斤)'
  },
  blockchain_tx_hash: {
    type: DataTypes.STRING(66),
    comment: '区块链交易哈希'
  },
  status: {
    type: DataTypes.ENUM('planted', 'harvested', 'processed', 'distributed', 'sold'),
    defaultValue: 'planted'
  }
}, {
  tableName: 'coffee_batches',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 定义关联关系
User.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });
Role.hasMany(User, { foreignKey: 'role_id', as: 'users' });

RegistrationApplication.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });
RegistrationApplication.belongsTo(Admin, { foreignKey: 'reviewed_by', as: 'reviewer' });

CoffeeBatch.belongsTo(User, { foreignKey: 'grower_id', as: 'grower' });
User.hasMany(CoffeeBatch, { foreignKey: 'grower_id', as: 'batches' });

module.exports = {
  sequelize,
  Role,
  User,
  RegistrationApplication,
  Admin,
  CoffeeBatch
};
