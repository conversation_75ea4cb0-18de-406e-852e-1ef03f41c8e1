{"name": "@apollo/utils.sortast", "version": "1.1.0", "description": "Sort AST nodes in a document alphabetically", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-utils.git", "directory": "sortAst/"}, "keywords": ["apollo", "graphql", "typescript", "node"], "author": "Apollo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=12.13.0"}, "publishConfig": {"access": "public"}, "dependencies": {"lodash.sortby": "^4.7.0"}, "peerDependencies": {"graphql": "14.x || 15.x || 16.x"}}