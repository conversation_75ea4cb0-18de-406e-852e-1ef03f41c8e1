// Type definitions for lru-cache 7.10.0
// Project: https://github.com/isaacs/node-lru-cache
// Based initially on @types/lru-cache
// https://github.com/DefinitelyTyped/DefinitelyTyped
// used under the terms of the MIT License, shown below.
//
// DefinitelyTyped license:
// ------
// MIT License
//
// Copyright (c) Microsoft Corporation.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the "Software"),
// to deal in the Software without restriction, including without limitation
// the rights to use, copy, modify, merge, publish, distribute, sublicense,
// and/or sell copies of the Software, and to permit persons to whom the
// Software is furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
// EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
// IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
// CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
// TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
// SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE
// ------
//
// Changes by Isaac Z. Schlueter released under the terms found in the
// LICENSE file within this project.

/// <reference lib="DOM" />
//tslint:disable:member-access
declare class LRUCache<K, V> implements Iterable<[K, V]> {
  constructor(options: LRUCache.Options<K, V>)

  /**
   * Number of items in the cache.
   * Alias for `cache.size`
   *
   * @deprecated since 7.0 use `cache.size` instead
   */
  public readonly length: number

  public readonly max: number
  public readonly maxSize: number
  public readonly sizeCalculation:
    | LRUCache.SizeCalculator<K, V>
    | undefined
  public readonly dispose: LRUCache.Disposer<K, V>
  /**
   * @since 7.4.0
   */
  public readonly disposeAfter: LRUCache.Disposer<K, V> | null
  public readonly noDisposeOnSet: boolean
  public readonly ttl: number
  public readonly ttlResolution: number
  public readonly ttlAutopurge: boolean
  public readonly allowStale: boolean
  public readonly updateAgeOnGet: boolean
  /**
   * @since 7.11.0
   */
  public readonly noDeleteOnStaleGet: boolean
  /**
   * @since 7.6.0
   */
  public readonly fetchMethod: LRUCache.Fetcher<K, V> | null

  /**
   * The total number of items held in the cache at the current moment.
   */
  public readonly size: number

  /**
   * The total size of items in cache when using size tracking.
   */
  public readonly calculatedSize: number

  /**
   * Add a value to the cache.
   */
  public set(
    key: K,
    value: V,
    options?: LRUCache.SetOptions<K, V>
  ): this

  /**
   * Return a value from the cache.
   * Will update the recency of the cache entry found.
   * If the key is not found, `get()` will return `undefined`.
   * This can be confusing when setting values specifically to `undefined`,
   * as in `cache.set(key, undefined)`. Use `cache.has()` to determine
   * whether a key is present in the cache at all.
   */
  // tslint:disable-next-line:no-unnecessary-generics
  public get<T = V>(
    key: K,
    options?: LRUCache.GetOptions
  ): T | undefined

  /**
   * Like `get()` but doesn't update recency or delete stale items.
   * Returns `undefined` if the item is stale, unless `allowStale` is set
   * either on the cache or in the options object.
   */
  // tslint:disable-next-line:no-unnecessary-generics
  public peek<T = V>(
    key: K,
    options?: LRUCache.PeekOptions
  ): T | undefined

  /**
   * Check if a key is in the cache, without updating the recency of use.
   * Will return false if the item is stale, even though it is technically
   * in the cache.
   * Will not update item age unless `updateAgeOnHas` is set in the options
   * or constructor.
   */
  public has(key: K, options?: LRUCache.HasOptions): boolean

  /**
   * Deletes a key out of the cache.
   * Returns true if the key was deleted, false otherwise.
   */
  public delete(key: K): boolean

  /**
   * Clear the cache entirely, throwing away all values.
   */
  public clear(): void

  /**
   * Delete any stale entries. Returns true if anything was removed, false
   * otherwise.
   */
  public purgeStale(): boolean

  /**
   * Find a value for which the supplied fn method returns a truthy value,
   * similar to Array.find().  fn is called as fn(value, key, cache).
   */
  // tslint:disable-next-line:no-unnecessary-generics
  public find<T = V>(
    callbackFn: (
      value: V,
      key: K,
      cache: this
    ) => boolean | undefined | void,
    options?: LRUCache.GetOptions
  ): T

  /**
   * Call the supplied function on each item in the cache, in order from
   * most recently used to least recently used.  fn is called as
   * fn(value, key, cache).  Does not update age or recenty of use.
   */
  public forEach<T = this>(
    callbackFn: (this: T, value: V, key: K, cache: this) => void,
    thisArg?: T
  ): void

  /**
   * The same as `cache.forEach(...)` but items are iterated over in reverse
   * order.  (ie, less recently used items are iterated over first.)
   */
  public rforEach<T = this>(
    callbackFn: (this: T, value: V, key: K, cache: this) => void,
    thisArg?: T
  ): void

  /**
   * Return a generator yielding the keys in the cache,
   * in order from most recently used to least recently used.
   */
  public keys(): Generator<K>

  /**
   * Inverse order version of `cache.keys()`
   * Return a generator yielding the keys in the cache,
   * in order from least recently used to most recently used.
   */
  public rkeys(): Generator<K>

  /**
   * Return a generator yielding the values in the cache,
   * in order from most recently used to least recently used.
   */
  public values(): Generator<V>

  /**
   * Inverse order version of `cache.values()`
   * Return a generator yielding the values in the cache,
   * in order from least recently used to most recently used.
   */
  public rvalues(): Generator<V>

  /**
   * Return a generator yielding `[key, value]` pairs,
   * in order from most recently used to least recently used.
   */
  public entries(): Generator<[K, V]>

  /**
   * Inverse order version of `cache.entries()`
   * Return a generator yielding `[key, value]` pairs,
   * in order from least recently used to most recently used.
   */
  public rentries(): Generator<[K, V]>

  /**
   * Iterating over the cache itself yields the same results as
   * `cache.entries()`
   */
  public [Symbol.iterator](): Iterator<[K, V]>

  /**
   * Return an array of [key, entry] objects which can be passed to
   * cache.load()
   */
  public dump(): Array<[K, LRUCache.Entry<V>]>

  /**
   * Reset the cache and load in the items in entries in the order listed.
   * Note that the shape of the resulting cache may be different if the
   * same options are not used in both caches.
   */
  public load(
    cacheEntries: ReadonlyArray<[K, LRUCache.Entry<V>]>
  ): void

  /**
   * Evict the least recently used item, returning its value or `undefined`
   * if cache is empty.
   */
  public pop(): V | undefined

  /**
   * Deletes a key out of the cache.
   *
   * @deprecated since 7.0 use delete() instead
   */
  public del(key: K): boolean

  /**
   * Clear the cache entirely, throwing away all values.
   *
   * @deprecated since 7.0 use clear() instead
   */
  public reset(): void

  /**
   * Manually iterates over the entire cache proactively pruning old entries.
   *
   * @deprecated since 7.0 use purgeStale() instead
   */
  public prune(): boolean

  /**
   * since: 7.6.0
   */
  // tslint:disable-next-line:no-unnecessary-generics
  public fetch<ExpectedValue = V>(
    key: K,
    options?: LRUCache.FetchOptions<K, V>
  ): Promise<ExpectedValue | undefined>

  /**
   * since: 7.6.0
   */
  public getRemainingTTL(key: K): number
}

declare namespace LRUCache {
  type LRUMilliseconds = number
  type DisposeReason = 'evict' | 'set' | 'delete'

  type SizeCalculator<K, V> = (value: V, key: K) => number
  type Disposer<K, V> = (
    value: V,
    key: K,
    reason: DisposeReason
  ) => void
  type Fetcher<K, V> = (
    key: K,
    staleValue: V,
    options: FetcherOptions<K, V>
  ) => Promise<V | void | undefined> | V | void | undefined

  interface DeprecatedOptions<K, V> {
    /**
     * alias for ttl
     *
     * @deprecated since 7.0 use options.ttl instead
     */
    maxAge?: number

    /**
     * alias for sizeCalculation
     *
     * @deprecated since 7.0 use options.sizeCalculation instead
     */
    length?: SizeCalculator<K, V>

    /**
     * alias for allowStale
     *
     * @deprecated since 7.0 use options.allowStale instead
     */
    stale?: boolean
  }

  interface LimitedByCount {
    /**
     * The number of most recently used items to keep.
     * Note that we may store fewer items than this if maxSize is hit.
     */
    max: number
  }

  interface LimitedBySize<K, V> {
    /**
     * If you wish to track item size, you must provide a maxSize
     * note that we still will only keep up to max *actual items*,
     * if max is set, so size tracking may cause fewer than max items
     * to be stored.  At the extreme, a single item of maxSize size
     * will cause everything else in the cache to be dropped when it
     * is added.  Use with caution!
     * Note also that size tracking can negatively impact performance,
     * though for most cases, only minimally.
     */
    maxSize: number

    /**
     * Function to calculate size of items.  Useful if storing strings or
     * buffers or other items where memory size depends on the object itself.
     * Also note that oversized items do NOT immediately get dropped from
     * the cache, though they will cause faster turnover in the storage.
     */
    sizeCalculation?: SizeCalculator<K, V>
  }

  interface LimitedByTTL {
    /**
     * Max time in milliseconds for items to live in cache before they are
     * considered stale.  Note that stale items are NOT preemptively removed
     * by default, and MAY live in the cache, contributing to its LRU max,
     * long after they have expired.
     *
     * Also, as this cache is optimized for LRU/MRU operations, some of
     * the staleness/TTL checks will reduce performance, as they will incur
     * overhead by deleting items.
     *
     * Must be an integer number of ms, defaults to 0, which means "no TTL"
     */
    ttl: number

    /**
     * Boolean flag to tell the cache to not update the TTL when
     * setting a new value for an existing key (ie, when updating a value
     * rather than inserting a new value).  Note that the TTL value is
     * _always_ set (if provided) when adding a new entry into the cache.
     *
     * @default false
     * @since 7.4.0
     */
    noUpdateTTL?: boolean

    /**
     * Minimum amount of time in ms in which to check for staleness.
     * Defaults to 1, which means that the current time is checked
     * at most once per millisecond.
     *
     * Set to 0 to check the current time every time staleness is tested.
     * (This reduces performance, and is theoretically unnecessary.)
     *
     * Setting this to a higher value will improve performance somewhat
     * while using ttl tracking, albeit at the expense of keeping stale
     * items around a bit longer than intended.
     *
     * @default 1
     * @since 7.1.0
     */
    ttlResolution?: number

    /**
     * Preemptively remove stale items from the cache.
     * Note that this may significantly degrade performance,
     * especially if the cache is storing a large number of items.
     * It is almost always best to just leave the stale items in
     * the cache, and let them fall out as new items are added.
     *
     * Note that this means that allowStale is a bit pointless,
     * as stale items will be deleted almost as soon as they expire.
     *
     * Use with caution!
     *
     * @default false
     * @since 7.1.0
     */
    ttlAutopurge?: boolean

    /**
     * Return stale items from cache.get() before disposing of them.
     * Return stale values from cache.fetch() while performing a call
     * to the `fetchMethod` in the background.
     *
     * @default false
     */
    allowStale?: boolean

    /**
     * Update the age of items on cache.get(), renewing their TTL
     *
     * @default false
     */
    updateAgeOnGet?: boolean

    /**
     * Do not delete stale items when they are retrieved with cache.get()
     * Note that the get() return value will still be `undefined` unless
     * allowStale is true.
     *
     * @default false
     * @since 7.11.0
     */
    noDeleteOnStaleGet?: boolean

    /**
     * Update the age of items on cache.has(), renewing their TTL
     *
     * @default false
     */
    updateAgeOnHas?: boolean
  }

  type SafetyBounds<K, V> =
    | LimitedByCount
    | LimitedBySize<K, V>
    | LimitedByTTL

  // options shared by all three of the limiting scenarios
  interface SharedOptions<K, V> {
    /**
     * Function that is called on items when they are dropped from the cache.
     * This can be handy if you want to close file descriptors or do other
     * cleanup tasks when items are no longer accessible. Called with `key,
     * value`.  It's called before actually removing the item from the
     * internal cache, so it is *NOT* safe to re-add them.
     * Use `disposeAfter` if you wish to dispose items after they have been
     * full removed, when it is safe to add them back to the cache.
     */
    dispose?: Disposer<K, V>

    /**
     * The same as dispose, but called *after* the entry is completely
     * removed and the cache is once again in a clean state.  It is safe to
     * add an item right back into the cache at this point.
     * However, note that it is *very* easy to inadvertently create infinite
     * recursion this way.
     *
     * @since 7.3.0
     */
    disposeAfter?: Disposer<K, V>

    /**
     * Set to true to suppress calling the dispose() function if the entry
     * key is still accessible within the cache.
     * This may be overridden by passing an options object to cache.set().
     *
     * @default false
     */
    noDisposeOnSet?: boolean

    /**
     * `fetchMethod` Function that is used to make background asynchronous
     * fetches.  Called with `fetchMethod(key, staleValue)`.  May return a
     * Promise.
     *
     * If `fetchMethod` is not provided, then `cache.fetch(key)` is
     * equivalent to `Promise.resolve(cache.get(key))`.
     *
     * @since 7.6.0
     */
    fetchMethod?: LRUCache.Fetcher<K, V>

    /**
     * Set to true to suppress the deletion of stale data when a
     * `fetchMethod` throws an error or returns a rejected promise
     *
     * @default false
     * @since 7.10.0
     */
    noDeleteOnFetchRejection?: boolean

    /**
     * Set to any value in the constructor or fetch() options to
     * pass arbitrary data to the fetch() method in the options.context
     * field.
     *
     * @since 7.12.0
     */
    fetchContext?: any
  }

  type Options<K, V> = SharedOptions<K, V> &
    DeprecatedOptions<K, V> &
    SafetyBounds<K, V>

  /**
   * options which override the options set in the LRUCache constructor
   * when making `cache.set()` calls.
   */
  interface SetOptions<K, V> {
    /**
     * A value for the size of the entry, prevents calls to
     * `sizeCalculation` function.
     */
    size?: number
    sizeCalculation?: SizeCalculator<K, V>
    ttl?: number
    start?: number
    noDisposeOnSet?: boolean
    noUpdateTTL?: boolean
  }

  /**
   * options which override the options set in the LRUCAche constructor
   * when making `cache.has()` calls.
   */
  interface HasOptions {
    updateAgeOnHas?: boolean
  }

  /**
   * options which override the options set in the LRUCache constructor
   * when making `cache.get()` calls.
   */
  interface GetOptions {
    allowStale?: boolean
    updateAgeOnGet?: boolean
    noDeleteOnStaleGet?: boolean
  }

  /**
   * options which override the options set in the LRUCache constructor
   * when making `cache.peek()` calls.
   */
  interface PeekOptions {
    allowStale?: boolean
  }

  interface FetcherFetchOptions<K, V> {
    allowStale?: boolean
    updateAgeOnGet?: boolean
    noDeleteOnStaleGet?: boolean
    size?: number
    sizeCalculation?: SizeCalculator<K, V>
    ttl?: number
    noDisposeOnSet?: boolean
    noUpdateTTL?: boolean
    noDeleteOnFetchRejection?: boolean
  }

  /**
   * options which override the options set in the LRUCache constructor
   * when making `cache.fetch()` calls.
   * This is the union of GetOptions and SetOptions, plus
   * `noDeleteOnFetchRejection`, `forceRefresh`, and `fetchContext`
   */
  interface FetchOptions<K, V> extends FetcherFetchOptions<K, V> {
    forceRefresh?: boolean
    fetchContext?: any
  }

  interface FetcherOptions<K, V> {
    signal: AbortSignal
    options: FetcherFetchOptions<K, V>
    context: any
  }

  interface Entry<V> {
    value: V
    ttl?: number
    size?: number
    start?: number
  }
}

export = LRUCache
