const Web3 = require('web3');
require('dotenv').config();

// 智能合约ABI (需要在部署后更新)
const contractABI = [
  {
    "inputs": [],
    "stateMutability": "nonpayable",
    "type": "constructor"
  },
  {
    "anonymous": false,
    "inputs": [
      {
        "indexed": true,
        "internalType": "string",
        "name": "batchNumber",
        "type": "string"
      },
      {
        "indexed": true,
        "internalType": "address",
        "name": "grower",
        "type": "address"
      },
      {
        "indexed": false,
        "internalType": "uint256",
        "name": "timestamp",
        "type": "uint256"
      }
    ],
    "name": "BatchCreated",
    "type": "event"
  },
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "_user",
        "type": "address"
      }
    ],
    "name": "authorizeUser",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {
        "internalType": "string",
        "name": "_batchNumber",
        "type": "string"
      },
      {
        "internalType": "string",
        "name": "_variety",
        "type": "string"
      },
      {
        "internalType": "string",
        "name": "_originLocation",
        "type": "string"
      },
      {
        "internalType": "uint256",
        "name": "_plantingDate",
        "type": "uint256"
      },
      {
        "internalType": "uint256",
        "name": "_harvestDate",
        "type": "uint256"
      },
      {
        "internalType": "string",
        "name": "_processingMethod",
        "type": "string"
      },
      {
        "internalType": "string",
        "name": "_qualityGrade",
        "type": "string"
      },
      {
        "internalType": "uint256",
        "name": "_quantityKg",
        "type": "uint256"
      }
    ],
    "name": "createBatch",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {
        "internalType": "string",
        "name": "_batchNumber",
        "type": "string"
      }
    ],
    "name": "getBatch",
    "outputs": [
      {
        "components": [
          {
            "internalType": "string",
            "name": "batchNumber",
            "type": "string"
          },
          {
            "internalType": "address",
            "name": "grower",
            "type": "address"
          },
          {
            "internalType": "string",
            "name": "variety",
            "type": "string"
          },
          {
            "internalType": "string",
            "name": "originLocation",
            "type": "string"
          },
          {
            "internalType": "uint256",
            "name": "plantingDate",
            "type": "uint256"
          },
          {
            "internalType": "uint256",
            "name": "harvestDate",
            "type": "uint256"
          },
          {
            "internalType": "string",
            "name": "processingMethod",
            "type": "string"
          },
          {
            "internalType": "string",
            "name": "qualityGrade",
            "type": "string"
          },
          {
            "internalType": "uint256",
            "name": "quantityKg",
            "type": "uint256"
          },
          {
            "internalType": "enum CoffeeTraceability.BatchStatus",
            "name": "status",
            "type": "uint8"
          },
          {
            "internalType": "uint256",
            "name": "createdAt",
            "type": "uint256"
          },
          {
            "internalType": "bool",
            "name": "exists",
            "type": "bool"
          }
        ],
        "internalType": "struct CoffeeTraceability.CoffeeBatch",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

class BlockchainService {
  constructor() {
    this.web3 = new Web3(process.env.BLOCKCHAIN_NETWORK_URL);
    this.account = null;
    this.contract = null;
    this.init();
  }

  async init() {
    try {
      // 添加私钥账户
      if (process.env.BLOCKCHAIN_PRIVATE_KEY) {
        this.account = this.web3.eth.accounts.privateKeyToAccount(process.env.BLOCKCHAIN_PRIVATE_KEY);
        this.web3.eth.accounts.wallet.add(this.account);
        this.web3.eth.defaultAccount = this.account.address;
      }

      // 初始化合约实例
      if (process.env.CONTRACT_ADDRESS) {
        this.contract = new this.web3.eth.Contract(contractABI, process.env.CONTRACT_ADDRESS);
      }

      console.log('区块链服务初始化成功');
    } catch (error) {
      console.error('区块链服务初始化失败:', error);
    }
  }

  async deployContract() {
    try {
      const contract = new this.web3.eth.Contract(contractABI);
      const deploy = contract.deploy({
        data: '0x608060405234801561001057600080fd5b50...' // 合约字节码，需要编译后获取
      });

      const deployedContract = await deploy.send({
        from: this.account.address,
        gas: 3000000,
        gasPrice: '***********'
      });

      console.log('合约部署成功，地址:', deployedContract.options.address);
      return deployedContract.options.address;
    } catch (error) {
      console.error('合约部署失败:', error);
      throw error;
    }
  }

  async createBatch(batchData) {
    try {
      if (!this.contract) {
        throw new Error('合约未初始化');
      }

      const result = await this.contract.methods.createBatch(
        batchData.batchNumber,
        batchData.variety,
        batchData.originLocation,
        batchData.plantingDate,
        batchData.harvestDate,
        batchData.processingMethod,
        batchData.qualityGrade,
        batchData.quantityKg
      ).send({
        from: this.account.address,
        gas: 500000
      });

      return result.transactionHash;
    } catch (error) {
      console.error('创建批次失败:', error);
      throw error;
    }
  }

  async getBatch(batchNumber) {
    try {
      if (!this.contract) {
        throw new Error('合约未初始化');
      }

      const result = await this.contract.methods.getBatch(batchNumber).call();
      return result;
    } catch (error) {
      console.error('获取批次失败:', error);
      throw error;
    }
  }

  async authorizeUser(userAddress) {
    try {
      if (!this.contract) {
        throw new Error('合约未初始化');
      }

      const result = await this.contract.methods.authorizeUser(userAddress).send({
        from: this.account.address,
        gas: 100000
      });

      return result.transactionHash;
    } catch (error) {
      console.error('授权用户失败:', error);
      throw error;
    }
  }
}

module.exports = new BlockchainService();
