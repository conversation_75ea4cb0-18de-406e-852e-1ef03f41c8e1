// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`usageReportingSignature basic test 1`] = `"{user{name}}"`;

exports[`usageReportingSignature basic test with query 1`] = `"{user{name}}"`;

exports[`usageReportingSignature basic with operation name 1`] = `"query OpName{user{name}}"`;

exports[`usageReportingSignature fragment 1`] = `"fragment Bar on User{asd}{user{name...Bar}}"`;

exports[`usageReportingSignature fragments in various order 1`] = `"fragment Bar on User{asd}{user{name...Bar}}"`;

exports[`usageReportingSignature full test 1`] = `"fragment Bar on User{age@skip(if:$a)...Nested}fragment Nested on User{blah}query Foo($a:Boolean,$b:Int){user(age:0,name:\\"\\"){name tz...Bar...on User{bee hello}}}"`;

exports[`usageReportingSignature with various argument types 1`] = `"query OpName($a:[[Boolean!]!],$b:EnumType,$c:Int!){user{name(apple:$a,bag:$b,cat:$c)}}"`;

exports[`usageReportingSignature with various inline types 1`] = `"query OpName{user{name(apple:[],bag:{},cat:ENUM_VALUE)}}"`;
