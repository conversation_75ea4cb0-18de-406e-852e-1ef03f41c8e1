{"version": 3, "file": "buildServiceDefinition.js", "sourceRoot": "", "sources": ["../src/buildServiceDefinition.ts"], "names": [], "mappings": ";;;AAAA,qCAgBiB;AACjB,iDAA6D;AAE7D,uDAA8D;AAY9D,SAAS,SAAS,CAAI,GAAoC;IACxD,OAAO,IAAI,KAAK,EAAK,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;AACvC,CAAC;AAED,SAAgB,sBAAsB,CACpC,OAA+C;IAE/C,MAAM,MAAM,GAAmB,EAAE,CAAC;IAElC,MAAM,kBAAkB,GAEpB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAExB,MAAM,iBAAiB,GAEnB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAExB,MAAM,aAAa,GAEf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAExB,MAAM,iBAAiB,GAA2B,EAAE,CAAC;IACrD,MAAM,gBAAgB,GAA0B,EAAE,CAAC;IAEnD,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;QAC1B,IAAI,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,IAAA,wBAAc,EAAC,MAAM,CAAC,EAAE;YAC5C,MAAM,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;SAC/B;QACD,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE;YACpD,IAAI,IAAA,8BAAoB,EAAC,UAAU,CAAC,EAAE;gBACpC,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;gBAEvC,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE;oBAChC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAC/C;qBAAM;oBACL,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;iBAC7C;aACF;iBAAM,IAAI,IAAA,6BAAmB,EAAC,UAAU,CAAC,EAAE;gBAC1C,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;gBAEvC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,EAAE;oBAC/B,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAC9C;qBAAM;oBACL,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;iBAC5C;aACF;iBAAM,IAAI,UAAU,CAAC,IAAI,KAAK,cAAI,CAAC,oBAAoB,EAAE;gBACxD,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;gBAE5C,IAAI,aAAa,CAAC,aAAa,CAAC,EAAE;oBAChC,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAC/C;qBAAM;oBACL,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;iBAC7C;aACF;iBAAM,IAAI,UAAU,CAAC,IAAI,KAAK,cAAI,CAAC,iBAAiB,EAAE;gBACrD,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACpC;iBAAM,IAAI,UAAU,CAAC,IAAI,KAAK,cAAI,CAAC,gBAAgB,EAAE;gBACpD,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACnC;SACF;KACF;IAED,KAAK,MAAM,CAAC,QAAQ,EAAE,eAAe,CAAC,IAAI,MAAM,CAAC,OAAO,CACtD,kBAAkB,CACnB,EAAE;QACD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,MAAM,CAAC,IAAI,CACT,IAAI,sBAAY,CACd,SAAS,QAAQ,+BAA+B,EAChD,eAAe,CAChB,CACF,CAAC;SACH;KACF;IAED,KAAK,MAAM,CAAC,aAAa,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QACvE,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,MAAM,CAAC,IAAI,CACT,IAAI,sBAAY,CACd,cAAc,aAAa,+BAA+B,EAC1D,UAAU,CACX,CACF,CAAC;SACH;KACF;IAED,IAAI,gBAA+D,CAAC;IAEpE,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC/D,gBAAgB,GAAG,EAAE,CAAC;QAItB,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEzE,MAAM,cAAc,GAAG,SAAS,CAC9B,CAAC,gBAAgB,EAAE,GAAG,gBAAgB,CAAC;aACpC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC;aAClC,MAAM,CAAC,iCAAoB,CAAC,CAChC,CAAC;QAEF,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;YAC1C,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YAC/C,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;YAE1C,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;gBAC/B,MAAM,IAAI,sBAAY,CACpB,yBAAyB,SAAS,kBAAkB,EACpD,CAAC,gBAAgB,CAAC,CACnB,CAAC;aACH;YACD,IAAI,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,CAAC,EAAE;gBAClE,MAAM,IAAI,sBAAY,CACpB,aAAa,SAAS,UAAU,QAAQ,0BAA0B,EAClE,CAAC,gBAAgB,CAAC,CACnB,CAAC;aACH;YACD,gBAAgB,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC;SACxC;KACF;SAAM;QACL,gBAAgB,GAAG;YACjB,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,UAAU;YACpB,YAAY,EAAE,cAAc;SAC7B,CAAC;KACH;IAED,KAAK,MAAM,CAAC,QAAQ,EAAE,cAAc,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;QAC1E,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YACjC,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACtD,kBAAkB,CAAC,QAAQ,CAAC,GAAG;oBAC7B;wBACE,IAAI,EAAE,cAAI,CAAC,sBAAsB;wBACjC,IAAI,EAAE;4BACJ,IAAI,EAAE,cAAI,CAAC,IAAI;4BACf,KAAK,EAAE,QAAQ;yBAChB;qBACF;iBACF,CAAC;aACH;iBAAM;gBACL,MAAM,CAAC,IAAI,CACT,IAAI,sBAAY,CACd,uBAAuB,QAAQ,qDAAqD,EACpF,cAAc,CACf,CACF,CAAC;aACH;SACF;KACF;IAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACrB,OAAO,EAAE,MAAM,EAAE,CAAC;KACnB;IAED,IAAI;QACF,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACrE,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;QAE3D,IAAI,MAAM,GAAG,IAAA,wBAAc,EAAC;YAC1B,IAAI,EAAE,cAAI,CAAC,QAAQ;YACnB,WAAW,EAAE,CAAC,GAAG,eAAe,EAAE,GAAG,UAAU,CAAC;SACjD,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAEnE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAM,GAAG,IAAA,sBAAY,EAAC,MAAM,EAAE;gBAC5B,IAAI,EAAE,cAAI,CAAC,QAAQ;gBACnB,WAAW,EAAE,cAAc;aAC5B,CAAC,CAAC;SACJ;QAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI,MAAM,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS;gBAAE,SAAS;YAEpD,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;SAChD;QAED,OAAO,EAAE,MAAM,EAAE,CAAC;KACnB;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;KAC5B;AACH,CAAC;AAjLD,wDAiLC;AAED,SAAS,oBAAoB,CAC3B,MAAqB,EACrB,SAAkC;IAElC,KAAK,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QAChE,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,IAAA,sBAAY,EAAC,IAAI,CAAC;YAAE,SAAS;QAElC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAElC,KAAK,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACnE,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC7B,IAAY,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;gBACpD,SAAS;aACV;YAED,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;YAClC,IAAI,CAAC,KAAK;gBAAE,SAAS;YAErB,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;gBACrC,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC;aAC7B;iBAAM;gBACL,IAAI,WAAW,CAAC,OAAO,EAAE;oBACvB,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;iBACrC;gBACD,IAAI,WAAW,CAAC,SAAS,EAAE;oBACzB,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;iBACzC;aACF;SACF;KACF;AACH,CAAC"}