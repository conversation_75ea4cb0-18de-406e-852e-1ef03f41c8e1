const express = require('express');
const { body, param } = require('express-validator');
const {
  getRegistrationApplications,
  reviewRegistrationApplication,
  getUsers,
  updateUserStatus,
  deleteUser,
  getOnlineUsersStats,
  getSystemStats
} = require('../controllers/adminController');
const { authenticateAdmin } = require('../middleware/auth');

const router = express.Router();

// 所有管理员路由都需要认证
router.use(authenticateAdmin);

// 审核注册申请验证规则
const reviewApplicationValidation = [
  param('applicationId')
    .isInt({ min: 1 })
    .withMessage('无效的申请ID'),
  body('action')
    .isIn(['approve', 'reject'])
    .withMessage('操作类型必须是approve或reject'),
  body('adminComment')
    .optional()
    .isLength({ max: 500 })
    .withMessage('管理员备注长度不能超过500个字符')
];

// 更新用户状态验证规则
const updateUserStatusValidation = [
  param('userId')
    .isInt({ min: 1 })
    .withMessage('无效的用户ID'),
  body('status')
    .isIn(['active', 'suspended', 'rejected'])
    .withMessage('用户状态必须是active、suspended或rejected')
];

// 路由定义
router.get('/applications', getRegistrationApplications);
router.put('/applications/:applicationId/review', reviewApplicationValidation, reviewRegistrationApplication);
router.get('/users', getUsers);
router.put('/users/:userId/status', updateUserStatusValidation, updateUserStatus);
router.delete('/users/:userId', deleteUser);
router.get('/users/online', getOnlineUsersStats);
router.get('/stats', getSystemStats);

module.exports = router;
