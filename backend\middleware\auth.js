const jwt = require('jsonwebtoken');
const { User, Admin } = require('../models');

// 用户认证中间件
const authenticateUser = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: '访问被拒绝，需要提供token' 
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.userId, {
      include: [{ model: require('../models').Role, as: 'role' }]
    });

    if (!user) {
      return res.status(401).json({ 
        success: false, 
        message: '无效的token' 
      });
    }

    if (user.status !== 'active') {
      return res.status(401).json({ 
        success: false, 
        message: '账户未激活或已被暂停' 
      });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('用户认证失败:', error);
    res.status(401).json({ 
      success: false, 
      message: '无效的token' 
    });
  }
};

// 管理员认证中间件
const authenticateAdmin = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: '访问被拒绝，需要提供token' 
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_ADMIN_SECRET);
    const admin = await Admin.findByPk(decoded.adminId);

    if (!admin) {
      return res.status(401).json({ 
        success: false, 
        message: '无效的token' 
      });
    }

    if (!admin.is_active) {
      return res.status(401).json({ 
        success: false, 
        message: '管理员账户已被禁用' 
      });
    }

    req.admin = admin;
    next();
  } catch (error) {
    console.error('管理员认证失败:', error);
    res.status(401).json({ 
      success: false, 
      message: '无效的token' 
    });
  }
};

// 角色权限检查中间件
const checkRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: '用户未认证' 
      });
    }

    if (!allowedRoles.includes(req.user.role.name)) {
      return res.status(403).json({ 
        success: false, 
        message: '权限不足' 
      });
    }

    next();
  };
};

// 超级管理员权限检查
const checkSuperAdmin = (req, res, next) => {
  if (!req.admin) {
    return res.status(401).json({ 
      success: false, 
      message: '管理员未认证' 
    });
  }

  if (!req.admin.is_super_admin) {
    return res.status(403).json({ 
      success: false, 
      message: '需要超级管理员权限' 
    });
  }

  next();
};

module.exports = {
  authenticateUser,
  authenticateAdmin,
  checkRole,
  checkSuperAdmin
};
