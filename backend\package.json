{"name": "coffee-traceability-backend", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["coffee", "blockchain", "traceability"], "author": "", "license": "ISC", "type": "commonjs", "description": "区块链咖啡豆溯源平台后端服务", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "mysql2": "^3.14.3", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "web3": "^4.16.0"}, "devDependencies": {"@types/node": "^24.1.0", "nodemon": "^3.1.10"}}