{"name": "lru-cache", "description": "A cache object that deletes the least-recently-used items.", "version": "7.13.1", "author": "<PERSON> <<EMAIL>>", "keywords": ["mru", "lru", "cache"], "scripts": {"build": "", "size": "size-limit", "test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write ."}, "main": "index.js", "repository": "git://github.com/isaacs/node-lru-cache.git", "devDependencies": {"@size-limit/preset-small-lib": "^7.0.8", "@types/node": "^17.0.31", "@types/tap": "^15.0.6", "benchmark": "^2.1.4", "c8": "^7.11.2", "clock-mock": "^1.0.4", "eslint-config-prettier": "^8.5.0", "prettier": "^2.6.2", "size-limit": "^7.0.8", "tap": "^16.0.1", "ts-node": "^10.7.0", "tslib": "^2.4.0", "typescript": "^4.6.4"}, "license": "ISC", "files": ["index.js", "index.d.ts"], "engines": {"node": ">=12"}, "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"], "ts": false}, "size-limit": [{"path": "./index.js"}]}