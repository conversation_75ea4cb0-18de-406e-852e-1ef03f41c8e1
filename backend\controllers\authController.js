const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { User, Role, RegistrationApplication, Admin } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// 用户注册申请
const registerUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const {
      username,
      email,
      password,
      roleId,
      realName,
      phone,
      companyName,
      address,
      licenseNumber,
      applicationReason
    } = req.body;

    // 检查用户名和邮箱是否已存在
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [{ username }, { email }]
      }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名或邮箱已存在'
      });
    }

    // 检查是否已有待审核的申请
    const existingApplication = await RegistrationApplication.findOne({
      where: {
        [Op.or]: [{ username }, { email }],
        status: 'pending'
      }
    });

    if (existingApplication) {
      return res.status(400).json({
        success: false,
        message: '已有待审核的注册申请'
      });
    }

    // 加密密码
    const passwordHash = await bcrypt.hash(password, parseInt(process.env.BCRYPT_ROUNDS));

    // 创建注册申请
    const application = await RegistrationApplication.create({
      username,
      email,
      password_hash: passwordHash,
      role_id: roleId,
      real_name: realName,
      phone,
      company_name: companyName,
      address,
      license_number: licenseNumber,
      application_reason: applicationReason
    });

    res.status(201).json({
      success: true,
      message: '注册申请提交成功，请等待管理员审核',
      data: {
        applicationId: application.id,
        username: application.username,
        email: application.email,
        status: application.status
      }
    });

  } catch (error) {
    console.error('用户注册失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 用户登录
const loginUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { username, password } = req.body;

    // 查找用户
    const user = await User.findOne({
      where: { username },
      include: [{ model: Role, as: 'role' }]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '账户未激活或已被暂停'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 更新登录时间和在线状态
    await user.update({
      last_login_at: new Date(),
      is_online: true
    });

    // 生成JWT token
    const token = jwt.sign(
      { userId: user.id, username: user.username, role: user.role.name },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          realName: user.real_name,
          role: user.role,
          isOnline: user.is_online,
          lastLoginAt: user.last_login_at
        }
      }
    });

  } catch (error) {
    console.error('用户登录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 用户登出
const logoutUser = async (req, res) => {
  try {
    const user = req.user;
    
    // 更新离线时间和在线状态
    await user.update({
      last_offline_at: new Date(),
      is_online: false
    });

    res.json({
      success: true,
      message: '登出成功'
    });

  } catch (error) {
    console.error('用户登出失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 管理员登录
const loginAdmin = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { username, password } = req.body;

    // 查找管理员
    const admin = await Admin.findOne({
      where: { username }
    });

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 检查管理员状态
    if (!admin.is_active) {
      return res.status(401).json({
        success: false,
        message: '管理员账户已被禁用'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, admin.password_hash);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 更新登录时间
    await admin.update({
      last_login_at: new Date()
    });

    // 生成JWT token
    const token = jwt.sign(
      { adminId: admin.id, username: admin.username, isSuperAdmin: admin.is_super_admin },
      process.env.JWT_ADMIN_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    res.json({
      success: true,
      message: '管理员登录成功',
      data: {
        token,
        admin: {
          id: admin.id,
          username: admin.username,
          realName: admin.real_name,
          email: admin.email,
          isSuperAdmin: admin.is_super_admin,
          lastLoginAt: admin.last_login_at
        }
      }
    });

  } catch (error) {
    console.error('管理员登录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取当前用户信息
const getCurrentUser = async (req, res) => {
  try {
    const user = req.user;
    
    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        realName: user.real_name,
        phone: user.phone,
        companyName: user.company_name,
        address: user.address,
        licenseNumber: user.license_number,
        role: user.role,
        status: user.status,
        isOnline: user.is_online,
        lastLoginAt: user.last_login_at,
        blockchainAddress: user.blockchain_address
      }
    });

  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取所有角色
const getRoles = async (req, res) => {
  try {
    const roles = await Role.findAll({
      attributes: ['id', 'name', 'display_name', 'description']
    });

    res.json({
      success: true,
      data: roles
    });

  } catch (error) {
    console.error('获取角色列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

module.exports = {
  registerUser,
  loginUser,
  logoutUser,
  loginAdmin,
  getCurrentUser,
  getRoles
};
