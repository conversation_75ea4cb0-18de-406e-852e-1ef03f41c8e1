{"name": "xhr", "version": "2.6.0", "description": "small xhr abstraction", "keywords": ["xhr", "http", "xmlhttprequest", "xhr2", "browserify"], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/naugtur/xhr.git", "main": "index", "homepage": "https://github.com/naugtur/xhr", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/naugtur/xhr/issues", "email": "<EMAIL>"}, "typings": "./index.d.ts", "dependencies": {"global": "~4.4.0", "is-function": "^1.0.1", "parse-headers": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"for-each": "^0.3.2", "pre-commit": "1.2.2", "run-browser": "naugtur/run-browser", "tap-spec": "^4.0.2", "tape": "^4.0.0"}, "license": "MIT", "scripts": {"test": "run-browser test/index.js -b -m test/mock-server.js | tap-spec", "browser": "run-browser -m test/mock-server.js test/index.js"}}