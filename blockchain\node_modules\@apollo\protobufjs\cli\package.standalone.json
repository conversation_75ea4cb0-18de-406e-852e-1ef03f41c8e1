{"name": "protobufjs-cli", "description": "Translates between file formats and generates static code as well as TypeScript definitions.", "version": "6.7.0", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "bin": {"pbjs": "bin/pbjs", "pbts": "bin/pbts"}, "peerDependencies": {"@apollo/protobufjs": "~1.0.5"}, "dependencies": {"chalk": "^1.1.3", "escodegen": "^1.8.1", "espree": "^3.1.3", "estraverse": "^4.2.0", "glob": "^7.1.1", "jsdoc": "^3.4.2", "minimist": "^1.2.0", "semver": "^5.3.0", "tmp": "0.0.31", "uglify-js": "^2.8.15"}}