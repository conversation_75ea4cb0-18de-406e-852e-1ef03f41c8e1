/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */
import { sha256 } from '@noble/hashes/sha256';
import { getHash } from './_shortw_utils.js';
import { Field } from './abstract/modular.js';
import { weierstrass } from './abstract/weierstrass.js';
/**
 * bn254 pairing-friendly curve.
 * Previously known as alt_bn_128, when it had 128-bit security.
 * Barbulescu-Duquesne 2017 shown it's weaker: just about 100 bits,
 * so the naming has been adjusted to its prime bit count
 * https://hal.science/hal-01534101/file/main.pdf
 */
export const bn254 = weierstrass({
  a: BigInt(0),
  b: BigInt(3),
  Fp: Field(BigInt('0x30644e72e131a029b85045b68181585d97816a916871ca8d3c208c16d87cfd47')),
  n: BigInt('0x30644e72e131a029b85045b68181585d2833e84879b9709143e1f593f0000001'),
  Gx: BigInt(1),
  Gy: BigInt(2),
  h: BigInt(1),
  ...getHash(sha256),
});
