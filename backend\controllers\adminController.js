const { User, Role, RegistrationApplication, Admin } = require('../models');
const { validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const { Op } = require('sequelize');

// 获取注册申请列表
const getRegistrationApplications = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, roleId } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};
    if (status) whereClause.status = status;
    if (roleId) whereClause.role_id = roleId;

    const { count, rows } = await RegistrationApplication.findAndCountAll({
      where: whereClause,
      include: [
        { model: Role, as: 'role', attributes: ['id', 'name', 'display_name'] },
        { model: Admin, as: 'reviewer', attributes: ['id', 'username', 'real_name'] }
      ],
      order: [['applied_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        applications: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取注册申请列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 审核注册申请
const reviewRegistrationApplication = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { applicationId } = req.params;
    const { action, adminComment } = req.body; // action: 'approve' or 'reject'
    const admin = req.admin;

    const application = await RegistrationApplication.findByPk(applicationId, {
      include: [{ model: Role, as: 'role' }]
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: '注册申请不存在'
      });
    }

    if (application.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: '该申请已被审核'
      });
    }

    if (action === 'approve') {
      // 创建用户账户
      const user = await User.create({
        username: application.username,
        email: application.email,
        password_hash: application.password_hash,
        role_id: application.role_id,
        real_name: application.real_name,
        phone: application.phone,
        company_name: application.company_name,
        address: application.address,
        license_number: application.license_number,
        status: 'active'
      });

      // 更新申请状态
      await application.update({
        status: 'approved',
        admin_comment: adminComment,
        reviewed_at: new Date(),
        reviewed_by: admin.id
      });

      res.json({
        success: true,
        message: '注册申请已批准，用户账户创建成功',
        data: {
          userId: user.id,
          username: user.username
        }
      });

    } else if (action === 'reject') {
      // 拒绝申请
      await application.update({
        status: 'rejected',
        admin_comment: adminComment,
        reviewed_at: new Date(),
        reviewed_by: admin.id
      });

      res.json({
        success: true,
        message: '注册申请已拒绝'
      });

    } else {
      return res.status(400).json({
        success: false,
        message: '无效的操作类型'
      });
    }

  } catch (error) {
    console.error('审核注册申请失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取用户列表
const getUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, roleId, search } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};
    if (status) whereClause.status = status;
    if (roleId) whereClause.role_id = roleId;
    if (search) {
      whereClause[Op.or] = [
        { username: { [Op.like]: `%${search}%` } },
        { real_name: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await User.findAndCountAll({
      where: whereClause,
      include: [{ model: Role, as: 'role', attributes: ['id', 'name', 'display_name'] }],
      attributes: { exclude: ['password_hash'] },
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        users: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新用户状态
const updateUserStatus = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { userId } = req.params;
    const { status } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    await user.update({ status });

    res.json({
      success: true,
      message: '用户状态更新成功',
      data: {
        userId: user.id,
        username: user.username,
        status: user.status
      }
    });

  } catch (error) {
    console.error('更新用户状态失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除用户
const deleteUser = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    await user.destroy();

    res.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取在线用户统计
const getOnlineUsersStats = async (req, res) => {
  try {
    const onlineUsers = await User.findAll({
      where: { is_online: true },
      include: [{ model: Role, as: 'role', attributes: ['name', 'display_name'] }],
      attributes: ['id', 'username', 'real_name', 'last_login_at']
    });

    const totalUsers = await User.count();
    const onlineCount = onlineUsers.length;

    // 按角色统计在线用户
    const roleStats = {};
    onlineUsers.forEach(user => {
      const roleName = user.role.name;
      if (!roleStats[roleName]) {
        roleStats[roleName] = {
          displayName: user.role.display_name,
          count: 0,
          users: []
        };
      }
      roleStats[roleName].count++;
      roleStats[roleName].users.push({
        id: user.id,
        username: user.username,
        realName: user.real_name,
        lastLoginAt: user.last_login_at
      });
    });

    res.json({
      success: true,
      data: {
        totalUsers,
        onlineCount,
        offlineCount: totalUsers - onlineCount,
        onlineUsers,
        roleStats
      }
    });

  } catch (error) {
    console.error('获取在线用户统计失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取系统统计信息
const getSystemStats = async (req, res) => {
  try {
    const totalUsers = await User.count();
    const activeUsers = await User.count({ where: { status: 'active' } });
    const pendingApplications = await RegistrationApplication.count({ where: { status: 'pending' } });
    
    // 按角色统计用户数量
    const roleStats = await User.findAll({
      attributes: ['role_id'],
      include: [{ model: Role, as: 'role', attributes: ['name', 'display_name'] }],
      group: ['role_id'],
      raw: true
    });

    const usersByRole = {};
    for (const role of roleStats) {
      const count = await User.count({ where: { role_id: role.role_id } });
      usersByRole[role['role.name']] = {
        displayName: role['role.display_name'],
        count
      };
    }

    res.json({
      success: true,
      data: {
        totalUsers,
        activeUsers,
        pendingApplications,
        usersByRole
      }
    });

  } catch (error) {
    console.error('获取系统统计信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

module.exports = {
  getRegistrationApplications,
  reviewRegistrationApplication,
  getUsers,
  updateUserStatus,
  deleteUser,
  getOnlineUsersStats,
  getSystemStats
};
